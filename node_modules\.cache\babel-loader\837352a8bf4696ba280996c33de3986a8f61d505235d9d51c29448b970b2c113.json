{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiFormGroup', slot);\n}\nconst formGroupClasses = generateUtilityClasses('MuiFormGroup', ['root', 'row', 'error']);\nexport default formGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getFormGroupUtilityClass", "slot", "formGroupClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/FormGroup/formGroupClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiFormGroup', slot);\n}\nconst formGroupClasses = generateUtilityClasses('MuiFormGroup', ['root', 'row', 'error']);\nexport default formGroupClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACzF,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}