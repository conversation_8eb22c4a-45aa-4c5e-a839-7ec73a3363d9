{"ast": null, "code": "export { default } from '@mui/system/useMediaQuery';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/useMediaQuery/index.js"], "sourcesContent": ["export { default } from '@mui/system/useMediaQuery';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}