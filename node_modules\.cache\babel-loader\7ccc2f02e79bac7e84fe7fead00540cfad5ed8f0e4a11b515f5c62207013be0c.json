{"ast": null, "code": "'use client';\n\nexport { default } from './Input';\nexport { default as inputClasses } from './inputClasses';\nexport * from './inputClasses';", "map": {"version": 3, "names": ["default", "inputClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Input/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Input';\nexport { default as inputClasses } from './inputClasses';\nexport * from './inputClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}