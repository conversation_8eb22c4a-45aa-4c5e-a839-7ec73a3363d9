{"ast": null, "code": "var isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n  return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n  return target.concat(source).map(function (element) {\n    return cloneUnlessOtherwiseSpecified(element, options);\n  });\n}\nfunction mergeObject(target, source, options) {\n  var destination = {};\n  if (options.isMergeableObject(target)) {\n    Object.keys(target).forEach(function (key) {\n      destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n    });\n  }\n  Object.keys(source).forEach(function (key) {\n    if (!options.isMergeableObject(source[key]) || !target[key]) {\n      destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n    } else {\n      destination[key] = deepmerge(target[key], source[key], options);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, options) {\n  options = options || {};\n  options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n  options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneUnlessOtherwiseSpecified(source, options);\n  } else if (sourceIsArray) {\n    return options.arrayMerge(target, source, options);\n  } else {\n    return mergeObject(target, source, options);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n  if (!Array.isArray(array)) {\n    throw new Error('first argument should be an array');\n  }\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, options);\n  }, {});\n};\nvar deepmerge_1 = deepmerge;\nexport default deepmerge_1;", "map": {"version": 3, "names": ["isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "Object", "prototype", "toString", "call", "isReactElement", "canUseSymbol", "Symbol", "for", "REACT_ELEMENT_TYPE", "$$typeof", "emptyTarget", "val", "Array", "isArray", "cloneUnlessOtherwiseSpecified", "options", "clone", "deepmerge", "defaultArrayMerge", "target", "source", "concat", "map", "element", "mergeObject", "destination", "keys", "for<PERSON>ach", "key", "arrayMerge", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "all", "deepmergeAll", "array", "Error", "reduce", "prev", "next", "deepmerge_1"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/deepmerge/dist/es.js"], "sourcesContent": ["var isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tObject.keys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tObject.keys(source).forEach(function(key) {\n\t\tif (!options.isMergeableObject(source[key]) || !target[key]) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = deepmerge(target[key], source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nexport default deepmerge_1;\n"], "mappings": "AAAA,IAAIA,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EACzD,OAAOC,eAAe,CAACD,KAAK,CAAC,IACzB,CAACE,SAAS,CAACF,KAAK,CAAC;AACtB,CAAC;AAED,SAASC,eAAeA,CAACD,KAAK,EAAE;EAC/B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAC5C;AAEA,SAASE,SAASA,CAACF,KAAK,EAAE;EACzB,IAAIG,WAAW,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EAEvD,OAAOG,WAAW,KAAK,iBAAiB,IACpCA,WAAW,KAAK,eAAe,IAC/BK,cAAc,CAACR,KAAK,CAAC;AAC1B;;AAEA;AACA,IAAIS,YAAY,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;AAC7D,IAAIC,kBAAkB,GAAGH,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM;AAE5E,SAASH,cAAcA,CAACR,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACa,QAAQ,KAAKD,kBAAkB;AAC7C;AAEA,SAASE,WAAWA,CAACC,GAAG,EAAE;EACzB,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpC;AAEA,SAASG,6BAA6BA,CAAClB,KAAK,EAAEmB,OAAO,EAAE;EACtD,OAAQA,OAAO,CAACC,KAAK,KAAK,KAAK,IAAID,OAAO,CAACpB,iBAAiB,CAACC,KAAK,CAAC,GAChEqB,SAAS,CAACP,WAAW,CAACd,KAAK,CAAC,EAAEA,KAAK,EAAEmB,OAAO,CAAC,GAC7CnB,KAAK;AACT;AAEA,SAASsB,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EACnD,OAAOI,MAAM,CAACE,MAAM,CAACD,MAAM,CAAC,CAACE,GAAG,CAAC,UAASC,OAAO,EAAE;IAClD,OAAOT,6BAA6B,CAACS,OAAO,EAAER,OAAO,CAAC;EACvD,CAAC,CAAC;AACH;AAEA,SAASS,WAAWA,CAACL,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC7C,IAAIU,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIV,OAAO,CAACpB,iBAAiB,CAACwB,MAAM,CAAC,EAAE;IACtCnB,MAAM,CAAC0B,IAAI,CAACP,MAAM,CAAC,CAACQ,OAAO,CAAC,UAASC,GAAG,EAAE;MACzCH,WAAW,CAACG,GAAG,CAAC,GAAGd,6BAA6B,CAACK,MAAM,CAACS,GAAG,CAAC,EAAEb,OAAO,CAAC;IACvE,CAAC,CAAC;EACH;EACAf,MAAM,CAAC0B,IAAI,CAACN,MAAM,CAAC,CAACO,OAAO,CAAC,UAASC,GAAG,EAAE;IACzC,IAAI,CAACb,OAAO,CAACpB,iBAAiB,CAACyB,MAAM,CAACQ,GAAG,CAAC,CAAC,IAAI,CAACT,MAAM,CAACS,GAAG,CAAC,EAAE;MAC5DH,WAAW,CAACG,GAAG,CAAC,GAAGd,6BAA6B,CAACM,MAAM,CAACQ,GAAG,CAAC,EAAEb,OAAO,CAAC;IACvE,CAAC,MAAM;MACNU,WAAW,CAACG,GAAG,CAAC,GAAGX,SAAS,CAACE,MAAM,CAACS,GAAG,CAAC,EAAER,MAAM,CAACQ,GAAG,CAAC,EAAEb,OAAO,CAAC;IAChE;EACD,CAAC,CAAC;EACF,OAAOU,WAAW;AACnB;AAEA,SAASR,SAASA,CAACE,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAE;EAC3CA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvBA,OAAO,CAACc,UAAU,GAAGd,OAAO,CAACc,UAAU,IAAIX,iBAAiB;EAC5DH,OAAO,CAACpB,iBAAiB,GAAGoB,OAAO,CAACpB,iBAAiB,IAAIA,iBAAiB;EAE1E,IAAImC,aAAa,GAAGlB,KAAK,CAACC,OAAO,CAACO,MAAM,CAAC;EACzC,IAAIW,aAAa,GAAGnB,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC;EACzC,IAAIa,yBAAyB,GAAGF,aAAa,KAAKC,aAAa;EAE/D,IAAI,CAACC,yBAAyB,EAAE;IAC/B,OAAOlB,6BAA6B,CAACM,MAAM,EAAEL,OAAO,CAAC;EACtD,CAAC,MAAM,IAAIe,aAAa,EAAE;IACzB,OAAOf,OAAO,CAACc,UAAU,CAACV,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EACnD,CAAC,MAAM;IACN,OAAOS,WAAW,CAACL,MAAM,EAAEC,MAAM,EAAEL,OAAO,CAAC;EAC5C;AACD;AAEAE,SAAS,CAACgB,GAAG,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAEpB,OAAO,EAAE;EACrD,IAAI,CAACH,KAAK,CAACC,OAAO,CAACsB,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EACrD;EAEA,OAAOD,KAAK,CAACE,MAAM,CAAC,UAASC,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAOtB,SAAS,CAACqB,IAAI,EAAEC,IAAI,EAAExB,OAAO,CAAC;EACtC,CAAC,EAAE,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAIyB,WAAW,GAAGvB,SAAS;AAE3B,eAAeuB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}