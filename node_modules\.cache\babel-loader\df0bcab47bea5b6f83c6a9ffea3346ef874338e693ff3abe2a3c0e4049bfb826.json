{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Container, Paper, Typography, Button, Box, CircularProgress, Snackbar, Alert, Avatar } from '@mui/material';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userData$name, _userData$name$charAt;\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n  const [userData, setUserData] = useState(null);\n  useEffect(() => {\n    fetchProfile();\n  }, []);\n  const fetchProfile = async () => {\n    try {\n      const data = await authService.getProfile();\n      setUserData(data);\n    } catch (err) {\n      setError(err.message || 'Failed to fetch profile');\n      setShowError(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = () => {\n    authService.logout();\n    navigate('/login');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"sm\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4,\n        mt: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            width: 100,\n            height: 100,\n            mb: 2,\n            bgcolor: 'primary.main'\n          },\n          children: (userData === null || userData === void 0 ? void 0 : (_userData$name = userData.name) === null || _userData$name === void 0 ? void 0 : (_userData$name$charAt = _userData$name.charAt(0)) === null || _userData$name$charAt === void 0 ? void 0 : _userData$name$charAt.toUpperCase()) || 'U'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          component: \"h1\",\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), userData && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%',\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this), \" \", userData.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), \" \", userData.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"secondary\",\n          onClick: handleLogout,\n          sx: {\n            mt: 3\n          },\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: () => setShowError(false),\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        onClose: () => setShowError(false),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"eHP4j5rkdia//Z3PS16COuw/dXs=\", false, function () {\n  return [useNavigate];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "Container", "Paper", "Typography", "<PERSON><PERSON>", "Box", "CircularProgress", "Snackbar", "<PERSON><PERSON>", "Avatar", "authService", "jsxDEV", "_jsxDEV", "Profile", "_s", "_userData$name", "_userData$name$charAt", "navigate", "loading", "setLoading", "error", "setError", "showError", "setShowError", "userData", "setUserData", "fetchProfile", "data", "getProfile", "err", "message", "handleLogout", "logout", "sx", "display", "justifyContent", "mt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "max<PERSON><PERSON><PERSON>", "elevation", "p", "flexDirection", "alignItems", "width", "height", "mb", "bgcolor", "name", "char<PERSON>t", "toUpperCase", "variant", "gutterBottom", "email", "color", "onClick", "open", "autoHideDuration", "onClose", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/pages/Profile.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  Button,\r\n  Box,\r\n  CircularProgress,\r\n  Snackbar,\r\n  Alert,\r\n  Avatar\r\n} from '@mui/material';\r\nimport authService from '../api/authService';\r\n\r\nconst Profile = () => {\r\n  const navigate = useNavigate();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n  const [userData, setUserData] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchProfile();\r\n  }, []);\r\n\r\n  const fetchProfile = async () => {\r\n    try {\r\n      const data = await authService.getProfile();\r\n      setUserData(data);\r\n    } catch (err) {\r\n      setError(err.message || 'Failed to fetch profile');\r\n      setShowError(true);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    authService.logout();\r\n    navigate('/login');\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Container sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>\r\n        <CircularProgress />\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container component=\"main\" maxWidth=\"sm\">\r\n      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\r\n          <Avatar\r\n            sx={{\r\n              width: 100,\r\n              height: 100,\r\n              mb: 2,\r\n              bgcolor: 'primary.main'\r\n            }}\r\n          >\r\n            {userData?.name?.charAt(0)?.toUpperCase() || 'U'}\r\n          </Avatar>\r\n          <Typography component=\"h1\" variant=\"h5\" gutterBottom>\r\n            Profile\r\n          </Typography>\r\n          {userData && (\r\n            <Box sx={{ width: '100%', mt: 2 }}>\r\n              <Typography variant=\"body1\" gutterBottom>\r\n                <strong>Name:</strong> {userData.name}\r\n              </Typography>\r\n              <Typography variant=\"body1\" gutterBottom>\r\n                <strong>Email:</strong> {userData.email}\r\n              </Typography>\r\n              {/* Add more user data fields as needed */}\r\n            </Box>\r\n          )}\r\n          <Button\r\n            variant=\"contained\"\r\n            color=\"secondary\"\r\n            onClick={handleLogout}\r\n            sx={{ mt: 3 }}\r\n          >\r\n            Logout\r\n          </Button>\r\n        </Box>\r\n      </Paper>\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={() => setShowError(false)}\r\n      >\r\n        <Alert severity=\"error\" onClose={() => setShowError(false)}>\r\n          {error}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Profile; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,qBAAA;EACpB,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd2B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMjB,WAAW,CAACkB,UAAU,CAAC,CAAC;MAC3CH,WAAW,CAACE,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,CAACC,OAAO,IAAI,yBAAyB,CAAC;MAClDP,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBrB,WAAW,CAACsB,MAAM,CAAC,CAAC;IACpBf,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,IAAIC,OAAO,EAAE;IACX,oBACEN,OAAA,CAACX,SAAS;MAACgC,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAClEzB,OAAA,CAACN,gBAAgB;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEhB;EAEA,oBACE7B,OAAA,CAACX,SAAS;IAACyC,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAN,QAAA,gBACvCzB,OAAA,CAACV,KAAK;MAAC0C,SAAS,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEY,CAAC,EAAE,CAAC;QAAET,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACvCzB,OAAA,CAACP,GAAG;QAAC4B,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEY,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAV,QAAA,gBAC1EzB,OAAA,CAACH,MAAM;UACLwB,EAAE,EAAE;YACFe,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAE;UACX,CAAE;UAAAd,QAAA,EAED,CAAAb,QAAQ,aAARA,QAAQ,wBAAAT,cAAA,GAARS,QAAQ,CAAE4B,IAAI,cAAArC,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBsC,MAAM,CAAC,CAAC,CAAC,cAAArC,qBAAA,uBAAzBA,qBAAA,CAA2BsC,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACT7B,OAAA,CAACT,UAAU;UAACuC,SAAS,EAAC,IAAI;UAACa,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAnB,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZjB,QAAQ,iBACPZ,OAAA,CAACP,GAAG;UAAC4B,EAAE,EAAE;YAAEe,KAAK,EAAE,MAAM;YAAEZ,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAChCzB,OAAA,CAACT,UAAU;YAACoD,OAAO,EAAC,OAAO;YAACC,YAAY;YAAAnB,QAAA,gBACtCzB,OAAA;cAAAyB,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,QAAQ,CAAC4B,IAAI;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACb7B,OAAA,CAACT,UAAU;YAACoD,OAAO,EAAC,OAAO;YAACC,YAAY;YAAAnB,QAAA,gBACtCzB,OAAA;cAAAyB,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,QAAQ,CAACiC,KAAK;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN,eACD7B,OAAA,CAACR,MAAM;UACLmD,OAAO,EAAC,WAAW;UACnBG,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAE5B,YAAa;UACtBE,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EACf;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACR7B,OAAA,CAACL,QAAQ;MACPqD,IAAI,EAAEtC,SAAU;MAChBuC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,KAAK,CAAE;MAAAc,QAAA,eAEnCzB,OAAA,CAACJ,KAAK;QAACuD,QAAQ,EAAC,OAAO;QAACD,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,KAAK,CAAE;QAAAc,QAAA,EACxDjB;MAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAC3B,EAAA,CArFID,OAAO;EAAA,QACMb,WAAW;AAAA;AAAAgE,EAAA,GADxBnD,OAAO;AAuFb,eAAeA,OAAO;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}