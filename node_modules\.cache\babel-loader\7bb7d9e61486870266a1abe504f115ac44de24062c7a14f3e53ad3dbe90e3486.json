{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\nimport { useFormik } from 'formik';\nimport * as Yup from 'yup';\nimport { Container, Paper, Typography, Button, Link, Box, CircularProgress, Snackbar, Alert } from '@mui/material';\nimport TextFieldWrapper from '../components/TextFieldWrapper';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst validationSchema = Yup.object({\n  name: Yup.string().required('Name is required'),\n  email: Yup.string().email('Invalid email address').required('Email is required'),\n  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),\n  confirmPassword: Yup.string().oneOf([Yup.ref('password'), null], 'Passwords must match').required('Confirm password is required')\n});\nconst Signup = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n  const formik = useFormik({\n    initialValues: {\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    },\n    validationSchema,\n    onSubmit: async values => {\n      setLoading(true);\n      try {\n        await authService.signup({\n          name: values.name,\n          email: values.email,\n          password: values.password\n        });\n        navigate('/login');\n      } catch (err) {\n        setError(err.message || 'Signup failed');\n        setShowError(true);\n      } finally {\n        setLoading(false);\n      }\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"xs\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4,\n        mt: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        component: \"h1\",\n        variant: \"h5\",\n        align: \"center\",\n        gutterBottom: true,\n        children: \"Sign Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: formik.handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"name\",\n          label: \"Full Name\",\n          value: formik.values.name,\n          onChange: formik.handleChange,\n          error: formik.touched.name && Boolean(formik.errors.name),\n          helperText: formik.touched.name && formik.errors.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"email\",\n          label: \"Email Address\",\n          value: formik.values.email,\n          onChange: formik.handleChange,\n          error: formik.touched.email && Boolean(formik.errors.email),\n          helperText: formik.touched.email && formik.errors.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"password\",\n          label: \"Password\",\n          type: \"password\",\n          value: formik.values.password,\n          onChange: formik.handleChange,\n          error: formik.touched.password && Boolean(formik.errors.password),\n          helperText: formik.touched.password && formik.errors.password\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"confirmPassword\",\n          label: \"Confirm Password\",\n          type: \"password\",\n          value: formik.values.confirmPassword,\n          onChange: formik.handleChange,\n          error: formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword),\n          helperText: formik.touched.confirmPassword && formik.errors.confirmPassword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 24\n          }, this) : 'Sign Up'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            component: RouterLink,\n            to: \"/login\",\n            variant: \"body2\",\n            children: \"Already have an account? Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: () => setShowError(false),\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        onClose: () => setShowError(false),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"42+WMuy0r3blxthFAzMyQiiODVE=\", false, function () {\n  return [useNavigate, useFormik];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "useNavigate", "Link", "RouterLink", "useFormik", "<PERSON><PERSON>", "Container", "Paper", "Typography", "<PERSON><PERSON>", "Box", "CircularProgress", "Snackbar", "<PERSON><PERSON>", "TextFieldWrapper", "authService", "jsxDEV", "_jsxDEV", "validationSchema", "object", "name", "string", "required", "email", "password", "min", "confirmPassword", "oneOf", "ref", "Signup", "_s", "navigate", "loading", "setLoading", "error", "setError", "showError", "setShowError", "formik", "initialValues", "onSubmit", "values", "signup", "err", "message", "component", "max<PERSON><PERSON><PERSON>", "children", "elevation", "sx", "p", "mt", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleSubmit", "label", "value", "onChange", "handleChange", "touched", "Boolean", "errors", "helperText", "type", "fullWidth", "mb", "disabled", "size", "textAlign", "to", "open", "autoHideDuration", "onClose", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/pages/Signup.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\r\nimport { useFormik } from 'formik';\r\nimport * as Yup from 'yup';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  Button,\r\n  Link,\r\n  Box,\r\n  CircularProgress,\r\n  Snackbar,\r\n  Alert\r\n} from '@mui/material';\r\nimport TextFieldWrapper from '../components/TextFieldWrapper';\r\nimport authService from '../api/authService';\r\n\r\nconst validationSchema = Yup.object({\r\n  name: Yup.string()\r\n    .required('Name is required'),\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  password: Yup.string()\r\n    .min(6, 'Password must be at least 6 characters')\r\n    .required('Password is required'),\r\n  confirmPassword: Yup.string()\r\n    .oneOf([Yup.ref('password'), null], 'Passwords must match')\r\n    .required('Confirm password is required')\r\n});\r\n\r\nconst Signup = () => {\r\n  const navigate = useNavigate();\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n\r\n  const formik = useFormik({\r\n    initialValues: {\r\n      name: '',\r\n      email: '',\r\n      password: '',\r\n      confirmPassword: ''\r\n    },\r\n    validationSchema,\r\n    onSubmit: async (values) => {\r\n      setLoading(true);\r\n      try {\r\n        await authService.signup({\r\n          name: values.name,\r\n          email: values.email,\r\n          password: values.password\r\n        });\r\n        navigate('/login');\r\n      } catch (err) {\r\n        setError(err.message || 'Signup failed');\r\n        setShowError(true);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  });\r\n\r\n  return (\r\n    <Container component=\"main\" maxWidth=\"xs\">\r\n      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>\r\n        <Typography component=\"h1\" variant=\"h5\" align=\"center\" gutterBottom>\r\n          Sign Up\r\n        </Typography>\r\n        <form onSubmit={formik.handleSubmit}>\r\n          <TextFieldWrapper\r\n            name=\"name\"\r\n            label=\"Full Name\"\r\n            value={formik.values.name}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.name && Boolean(formik.errors.name)}\r\n            helperText={formik.touched.name && formik.errors.name}\r\n          />\r\n          <TextFieldWrapper\r\n            name=\"email\"\r\n            label=\"Email Address\"\r\n            value={formik.values.email}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.email && Boolean(formik.errors.email)}\r\n            helperText={formik.touched.email && formik.errors.email}\r\n          />\r\n          <TextFieldWrapper\r\n            name=\"password\"\r\n            label=\"Password\"\r\n            type=\"password\"\r\n            value={formik.values.password}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.password && Boolean(formik.errors.password)}\r\n            helperText={formik.touched.password && formik.errors.password}\r\n          />\r\n          <TextFieldWrapper\r\n            name=\"confirmPassword\"\r\n            label=\"Confirm Password\"\r\n            type=\"password\"\r\n            value={formik.values.confirmPassword}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}\r\n            helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            fullWidth\r\n            variant=\"contained\"\r\n            sx={{ mt: 3, mb: 2 }}\r\n            disabled={loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} /> : 'Sign Up'}\r\n          </Button>\r\n          <Box sx={{ textAlign: 'center' }}>\r\n            <Link component={RouterLink} to=\"/login\" variant=\"body2\">\r\n              Already have an account? Login\r\n            </Link>\r\n          </Box>\r\n        </form>\r\n      </Paper>\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={() => setShowError(false)}\r\n      >\r\n        <Alert severity=\"error\" onClose={() => setShowError(false)}>\r\n          {error}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Signup; "], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,EAAEC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AAClE,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNP,IAAI,EACJQ,GAAG,EACHC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,gBAAgB,GAAGb,GAAG,CAACc,MAAM,CAAC;EAClCC,IAAI,EAAEf,GAAG,CAACgB,MAAM,CAAC,CAAC,CACfC,QAAQ,CAAC,kBAAkB,CAAC;EAC/BC,KAAK,EAAElB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAChBE,KAAK,CAAC,uBAAuB,CAAC,CAC9BD,QAAQ,CAAC,mBAAmB,CAAC;EAChCE,QAAQ,EAAEnB,GAAG,CAACgB,MAAM,CAAC,CAAC,CACnBI,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDH,QAAQ,CAAC,sBAAsB,CAAC;EACnCI,eAAe,EAAErB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACtB,GAAG,CAACuB,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC1DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,MAAMO,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMsC,MAAM,GAAGlC,SAAS,CAAC;IACvBmC,aAAa,EAAE;MACbnB,IAAI,EAAE,EAAE;MACRG,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZE,eAAe,EAAE;IACnB,CAAC;IACDR,gBAAgB;IAChBsB,QAAQ,EAAE,MAAOC,MAAM,IAAK;MAC1BR,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMlB,WAAW,CAAC2B,MAAM,CAAC;UACvBtB,IAAI,EAAEqB,MAAM,CAACrB,IAAI;UACjBG,KAAK,EAAEkB,MAAM,CAAClB,KAAK;UACnBC,QAAQ,EAAEiB,MAAM,CAACjB;QACnB,CAAC,CAAC;QACFO,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZR,QAAQ,CAACQ,GAAG,CAACC,OAAO,IAAI,eAAe,CAAC;QACxCP,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,CAAC;EAEF,oBACEhB,OAAA,CAACX,SAAS;IAACuC,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACvC9B,OAAA,CAACV,KAAK;MAACyC,SAAS,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACvC9B,OAAA,CAACT,UAAU;QAACqC,SAAS,EAAC,IAAI;QAACO,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,QAAQ;QAACC,YAAY;QAAAP,QAAA,EAAC;MAEpE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzC,OAAA;QAAMuB,QAAQ,EAAEF,MAAM,CAACqB,YAAa;QAAAZ,QAAA,gBAClC9B,OAAA,CAACH,gBAAgB;UACfM,IAAI,EAAC,MAAM;UACXwC,KAAK,EAAC,WAAW;UACjBC,KAAK,EAAEvB,MAAM,CAACG,MAAM,CAACrB,IAAK;UAC1B0C,QAAQ,EAAExB,MAAM,CAACyB,YAAa;UAC9B7B,KAAK,EAAEI,MAAM,CAAC0B,OAAO,CAAC5C,IAAI,IAAI6C,OAAO,CAAC3B,MAAM,CAAC4B,MAAM,CAAC9C,IAAI,CAAE;UAC1D+C,UAAU,EAAE7B,MAAM,CAAC0B,OAAO,CAAC5C,IAAI,IAAIkB,MAAM,CAAC4B,MAAM,CAAC9C;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACFzC,OAAA,CAACH,gBAAgB;UACfM,IAAI,EAAC,OAAO;UACZwC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAEvB,MAAM,CAACG,MAAM,CAAClB,KAAM;UAC3BuC,QAAQ,EAAExB,MAAM,CAACyB,YAAa;UAC9B7B,KAAK,EAAEI,MAAM,CAAC0B,OAAO,CAACzC,KAAK,IAAI0C,OAAO,CAAC3B,MAAM,CAAC4B,MAAM,CAAC3C,KAAK,CAAE;UAC5D4C,UAAU,EAAE7B,MAAM,CAAC0B,OAAO,CAACzC,KAAK,IAAIe,MAAM,CAAC4B,MAAM,CAAC3C;QAAM;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACFzC,OAAA,CAACH,gBAAgB;UACfM,IAAI,EAAC,UAAU;UACfwC,KAAK,EAAC,UAAU;UAChBQ,IAAI,EAAC,UAAU;UACfP,KAAK,EAAEvB,MAAM,CAACG,MAAM,CAACjB,QAAS;UAC9BsC,QAAQ,EAAExB,MAAM,CAACyB,YAAa;UAC9B7B,KAAK,EAAEI,MAAM,CAAC0B,OAAO,CAACxC,QAAQ,IAAIyC,OAAO,CAAC3B,MAAM,CAAC4B,MAAM,CAAC1C,QAAQ,CAAE;UAClE2C,UAAU,EAAE7B,MAAM,CAAC0B,OAAO,CAACxC,QAAQ,IAAIc,MAAM,CAAC4B,MAAM,CAAC1C;QAAS;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACFzC,OAAA,CAACH,gBAAgB;UACfM,IAAI,EAAC,iBAAiB;UACtBwC,KAAK,EAAC,kBAAkB;UACxBQ,IAAI,EAAC,UAAU;UACfP,KAAK,EAAEvB,MAAM,CAACG,MAAM,CAACf,eAAgB;UACrCoC,QAAQ,EAAExB,MAAM,CAACyB,YAAa;UAC9B7B,KAAK,EAAEI,MAAM,CAAC0B,OAAO,CAACtC,eAAe,IAAIuC,OAAO,CAAC3B,MAAM,CAAC4B,MAAM,CAACxC,eAAe,CAAE;UAChFyC,UAAU,EAAE7B,MAAM,CAAC0B,OAAO,CAACtC,eAAe,IAAIY,MAAM,CAAC4B,MAAM,CAACxC;QAAgB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACFzC,OAAA,CAACR,MAAM;UACL2D,IAAI,EAAC,QAAQ;UACbC,SAAS;UACTjB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEmB,EAAE,EAAE;UAAE,CAAE;UACrBC,QAAQ,EAAEvC,OAAQ;UAAAe,QAAA,EAEjBf,OAAO,gBAAGf,OAAA,CAACN,gBAAgB;YAAC6D,IAAI,EAAE;UAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACTzC,OAAA,CAACP,GAAG;UAACuC,EAAE,EAAE;YAAEwB,SAAS,EAAE;UAAS,CAAE;UAAA1B,QAAA,eAC/B9B,OAAA,CAACf,IAAI;YAAC2C,SAAS,EAAE1C,UAAW;YAACuE,EAAE,EAAC,QAAQ;YAACtB,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAEzD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACRzC,OAAA,CAACL,QAAQ;MACP+D,IAAI,EAAEvC,SAAU;MAChBwC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAAC,KAAK,CAAE;MAAAU,QAAA,eAEnC9B,OAAA,CAACJ,KAAK;QAACiE,QAAQ,EAAC,OAAO;QAACD,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAAC,KAAK,CAAE;QAAAU,QAAA,EACxDb;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAC5B,EAAA,CApGID,MAAM;EAAA,QACO5B,WAAW,EAKbG,SAAS;AAAA;AAAA2E,EAAA,GANpBlD,MAAM;AAsGZ,eAAeA,MAAM;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}