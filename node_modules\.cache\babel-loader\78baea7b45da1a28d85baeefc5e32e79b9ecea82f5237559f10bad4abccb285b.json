{"ast": null, "code": "'use client';\n\nexport { default } from './AppBar';\nexport { default as appBarClasses } from './appBarClasses';\nexport * from './appBarClasses';", "map": {"version": 3, "names": ["default", "appBarClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/AppBar/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './AppBar';\nexport { default as appBarClasses } from './appBarClasses';\nexport * from './appBarClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}