{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\components\\\\PrivateRoute.js\";\nimport { Navigate } from 'react-router-dom';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children\n}) => {\n  const isAuthenticated = authService.isAuthenticated();\n  return isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 39\n  }, this);\n};\n_c = PrivateRoute;\nexport default PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["Navigate", "authService", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "isAuthenticated", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/components/PrivateRoute.js"], "sourcesContent": ["import { Navigate } from 'react-router-dom';\r\nimport authService from '../api/authService';\r\n\r\nconst PrivateRoute = ({ children }) => {\r\n  const isAuthenticated = authService.isAuthenticated();\r\n  \r\n  return isAuthenticated ? children : <Navigate to=\"/login\" />;\r\n};\r\n\r\nexport default PrivateRoute; "], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAMC,eAAe,GAAGL,WAAW,CAACK,eAAe,CAAC,CAAC;EAErD,OAAOA,eAAe,GAAGD,QAAQ,gBAAGF,OAAA,CAACH,QAAQ;IAACO,EAAE,EAAC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9D,CAAC;AAACC,EAAA,GAJIR,YAAY;AAMlB,eAAeA,YAAY;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}