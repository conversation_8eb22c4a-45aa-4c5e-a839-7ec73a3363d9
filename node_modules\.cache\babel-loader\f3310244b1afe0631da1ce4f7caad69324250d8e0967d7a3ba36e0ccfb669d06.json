{"ast": null, "code": "import borders from './borders';\nimport display from './display';\nimport flexbox from './flexbox';\nimport grid from './cssGrid';\nimport positions from './positions';\nimport palette from './palette';\nimport shadows from './shadows';\nimport sizing from './sizing';\nimport spacing from './spacing';\nimport typography from './typography';\nconst filterPropsMapping = {\n  borders: borders.filterProps,\n  display: display.filterProps,\n  flexbox: flexbox.filterProps,\n  grid: grid.filterProps,\n  positions: positions.filterProps,\n  palette: palette.filterProps,\n  shadows: shadows.filterProps,\n  sizing: sizing.filterProps,\n  spacing: spacing.filterProps,\n  typography: typography.filterProps\n};\nexport const styleFunctionMapping = {\n  borders,\n  display,\n  flexbox,\n  grid,\n  positions,\n  palette,\n  shadows,\n  sizing,\n  spacing,\n  typography\n};\nexport const propToStyleFunction = Object.keys(filterPropsMapping).reduce((acc, styleFnName) => {\n  filterPropsMapping[styleFnName].forEach(propName => {\n    acc[propName] = styleFunctionMapping[styleFnName];\n  });\n  return acc;\n}, {});\nfunction getThemeValue(prop, value, theme) {\n  const inputProps = {\n    [prop]: value,\n    theme\n  };\n  const styleFunction = propToStyleFunction[prop];\n  return styleFunction ? styleFunction(inputProps) : {\n    [prop]: value\n  };\n}\nexport default getThemeValue;", "map": {"version": 3, "names": ["borders", "display", "flexbox", "grid", "positions", "palette", "shadows", "sizing", "spacing", "typography", "filterPropsMapping", "filterProps", "styleFunctionMapping", "propToStyleFunction", "Object", "keys", "reduce", "acc", "styleFnName", "for<PERSON>ach", "propName", "getThemeValue", "prop", "value", "theme", "inputProps", "styleFunction"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/system/esm/getThemeValue.js"], "sourcesContent": ["import borders from './borders';\nimport display from './display';\nimport flexbox from './flexbox';\nimport grid from './cssGrid';\nimport positions from './positions';\nimport palette from './palette';\nimport shadows from './shadows';\nimport sizing from './sizing';\nimport spacing from './spacing';\nimport typography from './typography';\nconst filterPropsMapping = {\n  borders: borders.filterProps,\n  display: display.filterProps,\n  flexbox: flexbox.filterProps,\n  grid: grid.filterProps,\n  positions: positions.filterProps,\n  palette: palette.filterProps,\n  shadows: shadows.filterProps,\n  sizing: sizing.filterProps,\n  spacing: spacing.filterProps,\n  typography: typography.filterProps\n};\nexport const styleFunctionMapping = {\n  borders,\n  display,\n  flexbox,\n  grid,\n  positions,\n  palette,\n  shadows,\n  sizing,\n  spacing,\n  typography\n};\nexport const propToStyleFunction = Object.keys(filterPropsMapping).reduce((acc, styleFnName) => {\n  filterPropsMapping[styleFnName].forEach(propName => {\n    acc[propName] = styleFunctionMapping[styleFnName];\n  });\n  return acc;\n}, {});\nfunction getThemeValue(prop, value, theme) {\n  const inputProps = {\n    [prop]: value,\n    theme\n  };\n  const styleFunction = propToStyleFunction[prop];\n  return styleFunction ? styleFunction(inputProps) : {\n    [prop]: value\n  };\n}\nexport default getThemeValue;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,kBAAkB,GAAG;EACzBV,OAAO,EAAEA,OAAO,CAACW,WAAW;EAC5BV,OAAO,EAAEA,OAAO,CAACU,WAAW;EAC5BT,OAAO,EAAEA,OAAO,CAACS,WAAW;EAC5BR,IAAI,EAAEA,IAAI,CAACQ,WAAW;EACtBP,SAAS,EAAEA,SAAS,CAACO,WAAW;EAChCN,OAAO,EAAEA,OAAO,CAACM,WAAW;EAC5BL,OAAO,EAAEA,OAAO,CAACK,WAAW;EAC5BJ,MAAM,EAAEA,MAAM,CAACI,WAAW;EAC1BH,OAAO,EAAEA,OAAO,CAACG,WAAW;EAC5BF,UAAU,EAAEA,UAAU,CAACE;AACzB,CAAC;AACD,OAAO,MAAMC,oBAAoB,GAAG;EAClCZ,OAAO;EACPC,OAAO;EACPC,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,OAAO;EACPC;AACF,CAAC;AACD,OAAO,MAAMI,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACL,kBAAkB,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,WAAW,KAAK;EAC9FR,kBAAkB,CAACQ,WAAW,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;IAClDH,GAAG,CAACG,QAAQ,CAAC,GAAGR,oBAAoB,CAACM,WAAW,CAAC;EACnD,CAAC,CAAC;EACF,OAAOD,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,SAASI,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACzC,MAAMC,UAAU,GAAG;IACjB,CAACH,IAAI,GAAGC,KAAK;IACbC;EACF,CAAC;EACD,MAAME,aAAa,GAAGb,mBAAmB,CAACS,IAAI,CAAC;EAC/C,OAAOI,aAAa,GAAGA,aAAa,CAACD,UAAU,CAAC,GAAG;IACjD,CAACH,IAAI,GAAGC;EACV,CAAC;AACH;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}