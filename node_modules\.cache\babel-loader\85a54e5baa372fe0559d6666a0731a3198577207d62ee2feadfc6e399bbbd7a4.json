{"ast": null, "code": "import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;", "map": {"version": 3, "names": ["ownerWindow"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/utils/ownerWindow.js"], "sourcesContent": ["import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}