{"ast": null, "code": "'use client';\n\nexport { default } from './ListItemText';\nexport { default as listItemTextClasses } from './listItemTextClasses';\nexport * from './listItemTextClasses';", "map": {"version": 3, "names": ["default", "listItemTextClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/ListItemText/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './ListItemText';\nexport { default as listItemTextClasses } from './listItemTextClasses';\nexport * from './listItemTextClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,mBAAmB,QAAQ,uBAAuB;AACtE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}