{"ast": null, "code": "import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === undefined && !(key in object)) {\n    baseAssignValue(object, key, value);\n  }\n}\nexport default assignValue;", "map": {"version": 3, "names": ["baseAssignValue", "eq", "objectProto", "Object", "prototype", "hasOwnProperty", "assignValue", "object", "key", "value", "objValue", "call", "undefined"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/lodash-es/_assignValue.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nexport default assignValue;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,EAAE,MAAM,SAAS;;AAExB;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACvC,IAAIC,QAAQ,GAAGH,MAAM,CAACC,GAAG,CAAC;EAC1B,IAAI,EAAEH,cAAc,CAACM,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,IAAIP,EAAE,CAACS,QAAQ,EAAED,KAAK,CAAC,CAAC,IACzDA,KAAK,KAAKG,SAAS,IAAI,EAAEJ,GAAG,IAAID,MAAM,CAAE,EAAE;IAC7CP,eAAe,CAACO,MAAM,EAAEC,GAAG,EAAEC,KAAK,CAAC;EACrC;AACF;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}