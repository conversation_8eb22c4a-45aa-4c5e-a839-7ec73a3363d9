{"ast": null, "code": "'use client';\n\nexport { default } from './Slider';\nexport * from './Slider';\nexport { default as sliderClasses } from './sliderClasses';\nexport * from './sliderClasses';", "map": {"version": 3, "names": ["default", "sliderClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Slider/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Slider';\nexport * from './Slider';\nexport { default as sliderClasses } from './sliderClasses';\nexport * from './sliderClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,cAAc,UAAU;AACxB,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}