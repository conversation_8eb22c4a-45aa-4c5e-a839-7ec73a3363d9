{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\components\\\\TextFieldWrapper.js\";\nimport { TextField } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextFieldWrapper = ({\n  name,\n  label,\n  type = 'text',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(TextField, {\n    fullWidth: true,\n    margin: \"normal\",\n    name: name,\n    label: label,\n    type: type,\n    variant: \"outlined\",\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = TextFieldWrapper;\nexport default TextFieldWrapper;\nvar _c;\n$RefreshReg$(_c, \"TextFieldWrapper\");", "map": {"version": 3, "names": ["TextField", "jsxDEV", "_jsxDEV", "TextFieldWrapper", "name", "label", "type", "props", "fullWidth", "margin", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/components/TextFieldWrapper.js"], "sourcesContent": ["import { TextField } from '@mui/material';\r\n\r\nconst TextFieldWrapper = ({ name, label, type = 'text', ...props }) => {\r\n  return (\r\n    <TextField\r\n      fullWidth\r\n      margin=\"normal\"\r\n      name={name}\r\n      label={label}\r\n      type={type}\r\n      variant=\"outlined\"\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport default TextFieldWrapper; "], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,IAAI,GAAG,MAAM;EAAE,GAAGC;AAAM,CAAC,KAAK;EACrE,oBACEL,OAAA,CAACF,SAAS;IACRQ,SAAS;IACTC,MAAM,EAAC,QAAQ;IACfL,IAAI,EAAEA,IAAK;IACXC,KAAK,EAAEA,KAAM;IACbC,IAAI,EAAEA,IAAK;IACXI,OAAO,EAAC,UAAU;IAAA,GACdH;EAAK;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACC,EAAA,GAZIZ,gBAAgB;AActB,eAAeA,gBAAgB;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}