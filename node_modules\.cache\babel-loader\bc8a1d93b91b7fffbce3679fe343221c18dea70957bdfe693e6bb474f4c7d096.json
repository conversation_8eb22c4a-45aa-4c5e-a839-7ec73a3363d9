{"ast": null, "code": "'use client';\n\nexport { default } from './LinearProgress';\nexport { default as linearProgressClasses } from './linearProgressClasses';\nexport * from './linearProgressClasses';", "map": {"version": 3, "names": ["default", "linearProgressClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/LinearProgress/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './LinearProgress';\nexport { default as linearProgressClasses } from './linearProgressClasses';\nexport * from './linearProgressClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}