{"ast": null, "code": "'use client';\n\nimport createTheme from './createTheme';\nconst defaultTheme = createTheme();\nexport default defaultTheme;", "map": {"version": 3, "names": ["createTheme", "defaultTheme"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/styles/defaultTheme.js"], "sourcesContent": ["'use client';\n\nimport createTheme from './createTheme';\nconst defaultTheme = createTheme();\nexport default defaultTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,WAAW,MAAM,eAAe;AACvC,MAAMC,YAAY,GAAGD,WAAW,CAAC,CAAC;AAClC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}