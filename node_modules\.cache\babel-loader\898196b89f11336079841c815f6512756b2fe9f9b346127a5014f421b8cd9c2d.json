{"ast": null, "code": "'use client';\n\nexport { default } from './CardActions';\nexport { default as cardActionsClasses } from './cardActionsClasses';\nexport * from './cardActionsClasses';", "map": {"version": 3, "names": ["default", "cardActionsClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/CardActions/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './CardActions';\nexport { default as cardActionsClasses } from './cardActionsClasses';\nexport * from './cardActionsClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}