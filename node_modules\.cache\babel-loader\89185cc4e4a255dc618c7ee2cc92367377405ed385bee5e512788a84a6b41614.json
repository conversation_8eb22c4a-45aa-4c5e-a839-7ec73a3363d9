{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8080/api/v1';\nconst authService = {\n  login: async credentials => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/login`, credentials);\n      if (response.data.token) {\n        localStorage.setItem('token', response.data.token);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      throw ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message;\n    }\n  },\n  signup: async userData => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/signup`, userData);\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  forgotPassword: async email => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/forgot-password`, {\n        email\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  getProfile: async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_BASE_URL}/profile`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n  },\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "authService", "login", "credentials", "response", "post", "data", "token", "localStorage", "setItem", "error", "_error$response", "message", "signup", "userData", "_error$response2", "forgotPassword", "email", "_error$response3", "getProfile", "getItem", "get", "headers", "Authorization", "_error$response4", "logout", "removeItem", "isAuthenticated"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/api/authService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = 'http://localhost:8080/api/v1';\r\n\r\nconst authService = {\r\n  login: async (credentials) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/login`, credentials);\r\n      if (response.data.token) {\r\n        localStorage.setItem('token', response.data.token);\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw error.response?.data || error.message;\r\n    }\r\n  },\r\n\r\n  signup: async (userData) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/signup`, userData);\r\n      return response.data;\r\n    } catch (error) {\r\n      throw error.response?.data || error.message;\r\n    }\r\n  },\r\n\r\n  forgotPassword: async (email) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/forgot-password`, { email });\r\n      return response.data;\r\n    } catch (error) {\r\n      throw error.response?.data || error.message;\r\n    }\r\n  },\r\n\r\n  getProfile: async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const response = await axios.get(`${API_BASE_URL}/profile`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      throw error.response?.data || error.message;\r\n    }\r\n  },\r\n\r\n  logout: () => {\r\n    localStorage.removeItem('token');\r\n  },\r\n\r\n  isAuthenticated: () => {\r\n    return !!localStorage.getItem('token');\r\n  }\r\n};\r\n\r\nexport default authService; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,8BAA8B;AAEnD,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAML,KAAK,CAACM,IAAI,CAAC,GAAGL,YAAY,QAAQ,EAAEG,WAAW,CAAC;MACvE,IAAIC,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAE;QACvBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;MACpD;MACA,OAAOH,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAC,eAAA;MACd,MAAM,EAAAA,eAAA,GAAAD,KAAK,CAACN,QAAQ,cAAAO,eAAA,uBAAdA,eAAA,CAAgBL,IAAI,KAAII,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDC,MAAM,EAAE,MAAOC,QAAQ,IAAK;IAC1B,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAML,KAAK,CAACM,IAAI,CAAC,GAAGL,YAAY,SAAS,EAAEc,QAAQ,CAAC;MACrE,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAK,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAL,KAAK,CAACN,QAAQ,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBT,IAAI,KAAII,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDI,cAAc,EAAE,MAAOC,KAAK,IAAK;IAC/B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAML,KAAK,CAACM,IAAI,CAAC,GAAGL,YAAY,kBAAkB,EAAE;QAAEiB;MAAM,CAAC,CAAC;MAC/E,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAQ,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAR,KAAK,CAACN,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBZ,IAAI,KAAII,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDO,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF,MAAMZ,KAAK,GAAGC,YAAY,CAACY,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMhB,QAAQ,GAAG,MAAML,KAAK,CAACsB,GAAG,CAAC,GAAGrB,YAAY,UAAU,EAAE;QAC1DsB,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUhB,KAAK;QAChC;MACF,CAAC,CAAC;MACF,OAAOH,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAc,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAd,KAAK,CAACN,QAAQ,cAAAoB,gBAAA,uBAAdA,gBAAA,CAAgBlB,IAAI,KAAII,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDa,MAAM,EAAEA,CAAA,KAAM;IACZjB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;EAClC,CAAC;EAEDC,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAO,CAAC,CAACnB,YAAY,CAACY,OAAO,CAAC,OAAO,CAAC;EACxC;AACF,CAAC;AAED,eAAenB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}