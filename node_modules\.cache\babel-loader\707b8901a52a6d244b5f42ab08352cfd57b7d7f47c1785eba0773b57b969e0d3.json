{"ast": null, "code": "import deepmerge from 'deepmerge';\nimport isPlainObject from 'lodash-es/isPlainObject';\nimport cloneDeep from 'lodash-es/cloneDeep';\nimport { createContext, useContext, Children, useRef, useEffect, useState, useCallback, useMemo, useImperativeHandle, createElement, useLayoutEffect, forwardRef, Component } from 'react';\nimport isEqual from 'react-fast-compare';\nimport invariant from 'tiny-warning';\nimport clone from 'lodash-es/clone';\nimport toPath from 'lodash-es/toPath';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nvar FormikContext = /*#__PURE__*/createContext(undefined);\nFormikContext.displayName = 'FormikContext';\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n  var formik = useContext(FormikContext);\n  !!!formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : invariant(false) : void 0;\n  return formik;\n}\n\n/** @private is the value an empty array? */\n\nvar isEmptyArray = function isEmptyArray(value) {\n  return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */\n\nvar isFunction = function isFunction(obj) {\n  return typeof obj === 'function';\n};\n/** @private is the given object an Object? */\n\nvar isObject = function isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n};\n/** @private is the given object an integer? */\n\nvar isInteger = function isInteger(obj) {\n  return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */\n\nvar isString = function isString(obj) {\n  return Object.prototype.toString.call(obj) === '[object String]';\n};\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\n\nvar isNaN$1 = function isNaN(obj) {\n  return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */\n\nvar isEmptyChildren = function isEmptyChildren(children) {\n  return Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */\n\nvar isPromise = function isPromise(value) {\n  return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */\n\nvar isInputEvent = function isInputEvent(value) {\n  return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */\n\nfunction getActiveElement(doc) {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */\n\nfunction getIn(obj, key, def, p) {\n  if (p === void 0) {\n    p = 0;\n  }\n  var path = toPath(key);\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  } // check if path is not in the end\n\n  if (p !== path.length && !obj) {\n    return def;\n  }\n  return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */\n\nfunction setIn(obj, path, value) {\n  var res = clone(obj); // this keeps inheritance when obj is a class\n\n  var resVal = res;\n  var i = 0;\n  var pathArray = toPath(path);\n  for (; i < pathArray.length - 1; i++) {\n    var currentPath = pathArray[i];\n    var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = clone(currentObj);\n    } else {\n      var nextPath = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  } // Return original object if new value is the same as current\n\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  } // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n  return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */\n\nfunction setNestedObjectValues(object, value, visited, response) {\n  if (visited === void 0) {\n    visited = new WeakMap();\n  }\n  if (response === void 0) {\n    response = {};\n  }\n  for (var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++) {\n    var k = _Object$keys[_i];\n    var val = object[k];\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true); // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n  return response;\n}\nfunction formikReducer(state, msg) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return _extends({}, state, {\n        values: msg.payload\n      });\n    case 'SET_TOUCHED':\n      return _extends({}, state, {\n        touched: msg.payload\n      });\n    case 'SET_ERRORS':\n      if (isEqual(state.errors, msg.payload)) {\n        return state;\n      }\n      return _extends({}, state, {\n        errors: msg.payload\n      });\n    case 'SET_STATUS':\n      return _extends({}, state, {\n        status: msg.payload\n      });\n    case 'SET_ISSUBMITTING':\n      return _extends({}, state, {\n        isSubmitting: msg.payload\n      });\n    case 'SET_ISVALIDATING':\n      return _extends({}, state, {\n        isValidating: msg.payload\n      });\n    case 'SET_FIELD_VALUE':\n      return _extends({}, state, {\n        values: setIn(state.values, msg.payload.field, msg.payload.value)\n      });\n    case 'SET_FIELD_TOUCHED':\n      return _extends({}, state, {\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n      });\n    case 'SET_FIELD_ERROR':\n      return _extends({}, state, {\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n      });\n    case 'RESET_FORM':\n      return _extends({}, state, msg.payload);\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n    case 'SUBMIT_ATTEMPT':\n      return _extends({}, state, {\n        touched: setNestedObjectValues(state.values, true),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1\n      });\n    case 'SUBMIT_FAILURE':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n    case 'SUBMIT_SUCCESS':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n    default:\n      return state;\n  }\n} // Initial empty states // objects\n\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n  var _ref$validateOnChange = _ref.validateOnChange,\n    validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange,\n    _ref$validateOnBlur = _ref.validateOnBlur,\n    validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur,\n    _ref$validateOnMount = _ref.validateOnMount,\n    validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount,\n    isInitialValid = _ref.isInitialValid,\n    _ref$enableReinitiali = _ref.enableReinitialize,\n    enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali,\n    onSubmit = _ref.onSubmit,\n    rest = _objectWithoutPropertiesLoose(_ref, [\"validateOnChange\", \"validateOnBlur\", \"validateOnMount\", \"isInitialValid\", \"enableReinitialize\", \"onSubmit\"]);\n  var props = _extends({\n    validateOnChange: validateOnChange,\n    validateOnBlur: validateOnBlur,\n    validateOnMount: validateOnMount,\n    onSubmit: onSubmit\n  }, rest);\n  var initialValues = useRef(props.initialValues);\n  var initialErrors = useRef(props.initialErrors || emptyErrors);\n  var initialTouched = useRef(props.initialTouched || emptyTouched);\n  var initialStatus = useRef(props.initialStatus);\n  var isMounted = useRef(false);\n  var fieldRegistry = useRef({});\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !(typeof isInitialValid === 'undefined') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.') : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  }\n  useEffect(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n  var _React$useState = useState(0),\n    setIteration = _React$useState[1];\n  var stateRef = useRef({\n    values: cloneDeep(props.initialValues),\n    errors: cloneDeep(props.initialErrors) || emptyErrors,\n    touched: cloneDeep(props.initialTouched) || emptyTouched,\n    status: cloneDeep(props.initialStatus),\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0\n  });\n  var state = stateRef.current;\n  var dispatch = useCallback(function (action) {\n    var prev = stateRef.current;\n    stateRef.current = formikReducer(prev, action); // force rerender\n\n    if (prev !== stateRef.current) setIteration(function (x) {\n      return x + 1;\n    });\n  }, []);\n  var runValidateHandler = useCallback(function (values, field) {\n    return new Promise(function (resolve, reject) {\n      var maybePromisedErrors = props.validate(values, field);\n      if (maybePromisedErrors == null) {\n        // use loose null check here on purpose\n        resolve(emptyErrors);\n      } else if (isPromise(maybePromisedErrors)) {\n        maybePromisedErrors.then(function (errors) {\n          resolve(errors || emptyErrors);\n        }, function (actualException) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n          }\n          reject(actualException);\n        });\n      } else {\n        resolve(maybePromisedErrors);\n      }\n    });\n  }, [props.validate]);\n  /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */\n\n  var runValidationSchema = useCallback(function (values, field) {\n    var validationSchema = props.validationSchema;\n    var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n    var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n    return new Promise(function (resolve, reject) {\n      promise.then(function () {\n        resolve(emptyErrors);\n      }, function (err) {\n        // Yup will throw a validation error if validation fails. We catch those and\n        // resolve them into Formik errors. We can sniff if something is a Yup error\n        // by checking error.name.\n        // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n        if (err.name === 'ValidationError') {\n          resolve(yupToFormErrors(err));\n        } else {\n          // We throw any other errors\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n          }\n          reject(err);\n        }\n      });\n    });\n  }, [props.validationSchema]);\n  var runSingleFieldLevelValidation = useCallback(function (field, value) {\n    return new Promise(function (resolve) {\n      return resolve(fieldRegistry.current[field].validate(value));\n    });\n  }, []);\n  var runFieldLevelValidations = useCallback(function (values) {\n    var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function (f) {\n      return isFunction(fieldRegistry.current[f].validate);\n    }); // Construct an array with all of the field validation functions\n\n    var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function (f) {\n      return runSingleFieldLevelValidation(f, getIn(values, f));\n    }) : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n    return Promise.all(fieldValidations).then(function (fieldErrorsList) {\n      return fieldErrorsList.reduce(function (prev, curr, index) {\n        if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n          return prev;\n        }\n        if (curr) {\n          prev = setIn(prev, fieldKeysWithValidation[index], curr);\n        }\n        return prev;\n      }, {});\n    });\n  }, [runSingleFieldLevelValidation]); // Run all validations and return the result\n\n  var runAllValidations = useCallback(function (values) {\n    return Promise.all([runFieldLevelValidations(values), props.validationSchema ? runValidationSchema(values) : {}, props.validate ? runValidateHandler(values) : {}]).then(function (_ref2) {\n      var fieldErrors = _ref2[0],\n        schemaErrors = _ref2[1],\n        validateErrors = _ref2[2];\n      var combinedErrors = deepmerge.all([fieldErrors, schemaErrors, validateErrors], {\n        arrayMerge: arrayMerge\n      });\n      return combinedErrors;\n    });\n  }, [props.validate, props.validationSchema, runFieldLevelValidations, runValidateHandler, runValidationSchema]); // Run all validations methods and update state accordingly\n\n  var validateFormWithHighPriority = useEventCallback(function (values) {\n    if (values === void 0) {\n      values = state.values;\n    }\n    dispatch({\n      type: 'SET_ISVALIDATING',\n      payload: true\n    });\n    return runAllValidations(values).then(function (combinedErrors) {\n      if (!!isMounted.current) {\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n        dispatch({\n          type: 'SET_ERRORS',\n          payload: combinedErrors\n        });\n      }\n      return combinedErrors;\n    });\n  });\n  useEffect(function () {\n    if (validateOnMount && isMounted.current === true && isEqual(initialValues.current, props.initialValues)) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n  var resetForm = useCallback(function (nextState) {\n    var values = nextState && nextState.values ? nextState.values : initialValues.current;\n    var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n    var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n    var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n    initialValues.current = values;\n    initialErrors.current = errors;\n    initialTouched.current = touched;\n    initialStatus.current = status;\n    var dispatchFn = function dispatchFn() {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          isSubmitting: !!nextState && !!nextState.isSubmitting,\n          errors: errors,\n          touched: touched,\n          status: status,\n          values: values,\n          isValidating: !!nextState && !!nextState.isValidating,\n          submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === 'number' ? nextState.submitCount : 0\n        }\n      });\n    };\n    if (props.onReset) {\n      var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n      if (isPromise(maybePromisedOnReset)) {\n        maybePromisedOnReset.then(dispatchFn);\n      } else {\n        dispatchFn();\n      }\n    } else {\n      dispatchFn();\n    }\n  }, [props.initialErrors, props.initialStatus, props.initialTouched, props.onReset]);\n  useEffect(function () {\n    if (isMounted.current === true && !isEqual(initialValues.current, props.initialValues)) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [enableReinitialize, props.initialValues, resetForm, validateOnMount, validateFormWithHighPriority]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialErrors.current, props.initialErrors)) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialTouched.current, props.initialTouched)) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialStatus.current, props.initialStatus)) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n  var validateField = useEventCallback(function (name) {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n    if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n      var value = getIn(state.values, name);\n      var maybePromise = fieldRegistry.current[name].validate(value);\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: true\n        });\n        return maybePromise.then(function (x) {\n          return x;\n        }).then(function (error) {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: {\n              field: name,\n              value: error\n            }\n          });\n          dispatch({\n            type: 'SET_ISVALIDATING',\n            payload: false\n          });\n        });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise\n          }\n        });\n        return Promise.resolve(maybePromise);\n      }\n    } else if (props.validationSchema) {\n      dispatch({\n        type: 'SET_ISVALIDATING',\n        payload: true\n      });\n      return runValidationSchema(state.values, name).then(function (x) {\n        return x;\n      }).then(function (error) {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: getIn(error, name)\n          }\n        });\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n      });\n    }\n    return Promise.resolve();\n  });\n  var registerField = useCallback(function (name, _ref3) {\n    var validate = _ref3.validate;\n    fieldRegistry.current[name] = {\n      validate: validate\n    };\n  }, []);\n  var unregisterField = useCallback(function (name) {\n    delete fieldRegistry.current[name];\n  }, []);\n  var setTouched = useEventCallback(function (touched, shouldValidate) {\n    dispatch({\n      type: 'SET_TOUCHED',\n      payload: touched\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var setErrors = useCallback(function (errors) {\n    dispatch({\n      type: 'SET_ERRORS',\n      payload: errors\n    });\n  }, []);\n  var setValues = useEventCallback(function (values, shouldValidate) {\n    var resolvedValues = isFunction(values) ? values(state.values) : values;\n    dispatch({\n      type: 'SET_VALUES',\n      payload: resolvedValues\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n  });\n  var setFieldError = useCallback(function (field, value) {\n    dispatch({\n      type: 'SET_FIELD_ERROR',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n  }, []);\n  var setFieldValue = useEventCallback(function (field, value, shouldValidate) {\n    dispatch({\n      type: 'SET_FIELD_VALUE',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n  });\n  var executeChange = useCallback(function (eventOrTextValue, maybePath) {\n    // By default, assume that the first argument is a string. This allows us to use\n    // handleChange with React Native and React Native Web's onChangeText prop which\n    // provides just the value of the input.\n    var field = maybePath;\n    var val = eventOrTextValue;\n    var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n    // so we handle like we would a normal HTML change event.\n\n    if (!isString(eventOrTextValue)) {\n      // If we can, persist the event\n      // @see https://reactjs.org/docs/events.html#event-pooling\n      if (eventOrTextValue.persist) {\n        eventOrTextValue.persist();\n      }\n      var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n      var type = target.type,\n        name = target.name,\n        id = target.id,\n        value = target.value,\n        checked = target.checked,\n        outerHTML = target.outerHTML,\n        options = target.options,\n        multiple = target.multiple;\n      field = maybePath ? maybePath : name ? name : id;\n      if (!field && process.env.NODE_ENV !== \"production\") {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n          handlerName: 'handleChange'\n        });\n      }\n      val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? '' : parsed) : /checkbox/.test(type) // checkboxes\n      ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n      ? getSelectedValues(options) : value;\n    }\n    if (field) {\n      // Set form fields by name\n      setFieldValue(field, val);\n    }\n  }, [setFieldValue, state.values]);\n  var handleChange = useEventCallback(function (eventOrPath) {\n    if (isString(eventOrPath)) {\n      return function (event) {\n        return executeChange(event, eventOrPath);\n      };\n    } else {\n      executeChange(eventOrPath);\n    }\n  });\n  var setFieldTouched = useEventCallback(function (field, touched, shouldValidate) {\n    if (touched === void 0) {\n      touched = true;\n    }\n    dispatch({\n      type: 'SET_FIELD_TOUCHED',\n      payload: {\n        field: field,\n        value: touched\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var executeBlur = useCallback(function (e, path) {\n    if (e.persist) {\n      e.persist();\n    }\n    var _e$target = e.target,\n      name = _e$target.name,\n      id = _e$target.id,\n      outerHTML = _e$target.outerHTML;\n    var field = path ? path : name ? name : id;\n    if (!field && process.env.NODE_ENV !== \"production\") {\n      warnAboutMissingIdentifier({\n        htmlContent: outerHTML,\n        documentationAnchorLink: 'handleblur-e-any--void',\n        handlerName: 'handleBlur'\n      });\n    }\n    setFieldTouched(field, true);\n  }, [setFieldTouched]);\n  var handleBlur = useEventCallback(function (eventOrString) {\n    if (isString(eventOrString)) {\n      return function (event) {\n        return executeBlur(event, eventOrString);\n      };\n    } else {\n      executeBlur(eventOrString);\n    }\n  });\n  var setFormikState = useCallback(function (stateOrCb) {\n    if (isFunction(stateOrCb)) {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: stateOrCb\n      });\n    } else {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: function payload() {\n          return stateOrCb;\n        }\n      });\n    }\n  }, []);\n  var setStatus = useCallback(function (status) {\n    dispatch({\n      type: 'SET_STATUS',\n      payload: status\n    });\n  }, []);\n  var setSubmitting = useCallback(function (isSubmitting) {\n    dispatch({\n      type: 'SET_ISSUBMITTING',\n      payload: isSubmitting\n    });\n  }, []);\n  var submitForm = useEventCallback(function () {\n    dispatch({\n      type: 'SUBMIT_ATTEMPT'\n    });\n    return validateFormWithHighPriority().then(function (combinedErrors) {\n      // In case an error was thrown and passed to the resolved Promise,\n      // `combinedErrors` can be an instance of an Error. We need to check\n      // that and abort the submit.\n      // If we don't do that, calling `Object.keys(new Error())` yields an\n      // empty array, which causes the validation to pass and the form\n      // to be submitted.\n      var isInstanceOfError = combinedErrors instanceof Error;\n      var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n      if (isActuallyValid) {\n        // Proceed with submit...\n        //\n        // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n        // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n        // This would be fine in simple cases, but make it impossible to disable submit\n        // buttons where people use callbacks or promises as side effects (which is basically\n        // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n        //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n        // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n        // cleanup of isSubmitting on behalf of the consumer.\n        var promiseOrUndefined;\n        try {\n          promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n          // via setSubmitting(false)\n\n          if (promiseOrUndefined === undefined) {\n            return;\n          }\n        } catch (error) {\n          throw error;\n        }\n        return Promise.resolve(promiseOrUndefined).then(function (result) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_SUCCESS'\n            });\n          }\n          return result;\n        })[\"catch\"](function (_errors) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_FAILURE'\n            }); // This is a legit error rejected by the onSubmit fn\n            // so we don't want to break the promise chain\n\n            throw _errors;\n          }\n        });\n      } else if (!!isMounted.current) {\n        // ^^^ Make sure Formik is still mounted before updating state\n        dispatch({\n          type: 'SUBMIT_FAILURE'\n        }); // throw combinedErrors;\n\n        if (isInstanceOfError) {\n          throw combinedErrors;\n        }\n      }\n      return;\n    });\n  });\n  var handleSubmit = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    } // Warn if form submission is triggered by a <button> without a\n    // specified `type` attribute during development. This mitigates\n    // a common gotcha in forms with both reset and submit buttons,\n    // where the dev forgets to add type=\"button\" to the reset button.\n\n    if (process.env.NODE_ENV !== \"production\" && typeof document !== 'undefined') {\n      // Safely get the active element (works with IE)\n      var activeElement = getActiveElement();\n      if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n        !(activeElement.attributes && activeElement.attributes.getNamedItem('type')) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : invariant(false) : void 0;\n      }\n    }\n    submitForm()[\"catch\"](function (reason) {\n      console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n    });\n  });\n  var imperativeMethods = {\n    resetForm: resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    setErrors: setErrors,\n    setFieldError: setFieldError,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    setFormikState: setFormikState,\n    submitForm: submitForm\n  };\n  var executeSubmit = useEventCallback(function () {\n    return onSubmit(state.values, imperativeMethods);\n  });\n  var handleReset = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n    resetForm();\n  });\n  var getFieldMeta = useCallback(function (name) {\n    return {\n      value: getIn(state.values, name),\n      error: getIn(state.errors, name),\n      touched: !!getIn(state.touched, name),\n      initialValue: getIn(initialValues.current, name),\n      initialTouched: !!getIn(initialTouched.current, name),\n      initialError: getIn(initialErrors.current, name)\n    };\n  }, [state.errors, state.touched, state.values]);\n  var getFieldHelpers = useCallback(function (name) {\n    return {\n      setValue: function setValue(value, shouldValidate) {\n        return setFieldValue(name, value, shouldValidate);\n      },\n      setTouched: function setTouched(value, shouldValidate) {\n        return setFieldTouched(name, value, shouldValidate);\n      },\n      setError: function setError(value) {\n        return setFieldError(name, value);\n      }\n    };\n  }, [setFieldValue, setFieldTouched, setFieldError]);\n  var getFieldProps = useCallback(function (nameOrOptions) {\n    var isAnObject = isObject(nameOrOptions);\n    var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n    var valueState = getIn(state.values, name);\n    var field = {\n      name: name,\n      value: valueState,\n      onChange: handleChange,\n      onBlur: handleBlur\n    };\n    if (isAnObject) {\n      var type = nameOrOptions.type,\n        valueProp = nameOrOptions.value,\n        is = nameOrOptions.as,\n        multiple = nameOrOptions.multiple;\n      if (type === 'checkbox') {\n        if (valueProp === undefined) {\n          field.checked = !!valueState;\n        } else {\n          field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n          field.value = valueProp;\n        }\n      } else if (type === 'radio') {\n        field.checked = valueState === valueProp;\n        field.value = valueProp;\n      } else if (is === 'select' && multiple) {\n        field.value = field.value || [];\n        field.multiple = true;\n      }\n    }\n    return field;\n  }, [handleBlur, handleChange, state.values]);\n  var dirty = useMemo(function () {\n    return !isEqual(initialValues.current, state.values);\n  }, [initialValues.current, state.values]);\n  var isValid = useMemo(function () {\n    return typeof isInitialValid !== 'undefined' ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n  }, [isInitialValid, dirty, state.errors, props]);\n  var ctx = _extends({}, state, {\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur: handleBlur,\n    handleChange: handleChange,\n    handleReset: handleReset,\n    handleSubmit: handleSubmit,\n    resetForm: resetForm,\n    setErrors: setErrors,\n    setFormikState: setFormikState,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setFieldError: setFieldError,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    submitForm: submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    isValid: isValid,\n    dirty: dirty,\n    unregisterField: unregisterField,\n    registerField: registerField,\n    getFieldProps: getFieldProps,\n    getFieldMeta: getFieldMeta,\n    getFieldHelpers: getFieldHelpers,\n    validateOnBlur: validateOnBlur,\n    validateOnChange: validateOnChange,\n    validateOnMount: validateOnMount\n  });\n  return ctx;\n}\nfunction Formik(props) {\n  var formikbag = useFormik(props);\n  var component = props.component,\n    children = props.children,\n    render = props.render,\n    innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n\n  useImperativeHandle(innerRef, function () {\n    return formikbag;\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !!props.render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  }\n  return createElement(FormikProvider, {\n    value: formikbag\n  }, component ? createElement(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n  ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? Children.only(children) : null : null);\n}\nfunction warnAboutMissingIdentifier(_ref4) {\n  var htmlContent = _ref4.htmlContent,\n    documentationAnchorLink = _ref4.documentationAnchorLink,\n    handlerName = _ref4.handlerName;\n  console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */\n\nfunction yupToFormErrors(yupError) {\n  var errors = {};\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n    for (var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n      var _ref5;\n      if (_isArray) {\n        if (_i >= _iterator.length) break;\n        _ref5 = _iterator[_i++];\n      } else {\n        _i = _iterator.next();\n        if (_i.done) break;\n        _ref5 = _i.value;\n      }\n      var err = _ref5;\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n  return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */\n\nfunction validateYupSchema(values, schema, sync, context) {\n  if (sync === void 0) {\n    sync = false;\n  }\n  var normalizedValues = prepareDataForValidation(values);\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues\n  });\n}\n/**\r\n * Recursively prepare values.\r\n */\n\nfunction prepareDataForValidation(values) {\n  var data = Array.isArray(values) ? [] : {};\n  for (var k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      var key = String(k);\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map(function (value) {\n          if (Array.isArray(value) === true || isPlainObject(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if (isPlainObject(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n  return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */\n\nfunction arrayMerge(target, source, options) {\n  var destination = target.slice();\n  source.forEach(function merge(e, i) {\n    if (typeof destination[i] === 'undefined') {\n      var cloneRequested = options.clone !== false;\n      var shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone ? deepmerge(Array.isArray(e) ? [] : {}, e, options) : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = deepmerge(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n/** Return multi select values based on an array of options */\n\nfunction getSelectedValues(options) {\n  return Array.from(options).filter(function (el) {\n    return el.selected;\n  }).map(function (el) {\n    return el.value;\n  });\n}\n/** Return the next value for a checkbox */\n\nfunction getValueForCheckbox(currentValue, checked, valueProp) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  } // If the currentValue was not a boolean we want to return an array\n\n  var currentArrayOfValues = [];\n  var isValueInArray = false;\n  var index = -1;\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n\n  return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\nfunction useEventCallback(fn) {\n  var ref = useRef(fn); // we copy a ref to the callback scoped to the current state/props on each render\n\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current.apply(void 0, args);\n  }, []);\n}\nfunction useField(propsOrFieldName) {\n  var formik = useFormikContext();\n  var getFieldProps = formik.getFieldProps,\n    getFieldMeta = formik.getFieldMeta,\n    getFieldHelpers = formik.getFieldHelpers,\n    registerField = formik.registerField,\n    unregisterField = formik.unregisterField;\n  var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n\n  var props = isAnObject ? propsOrFieldName : {\n    name: propsOrFieldName\n  };\n  var fieldName = props.name,\n    validateFn = props.validate;\n  useEffect(function () {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn\n      });\n    }\n    return function () {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n  if (process.env.NODE_ENV !== \"production\") {\n    !formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component') : invariant(false) : void 0;\n  }\n  !fieldName ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid field name. Either pass `useField` a string or an object containing a `name` key.') : invariant(false) : void 0;\n  var fieldHelpers = useMemo(function () {\n    return getFieldHelpers(fieldName);\n  }, [getFieldHelpers, fieldName]);\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\nfunction Field(_ref) {\n  var validate = _ref.validate,\n    name = _ref.name,\n    render = _ref.render,\n    children = _ref.children,\n    is = _ref.as,\n    component = _ref.component,\n    className = _ref.className,\n    props = _objectWithoutPropertiesLoose(_ref, [\"validate\", \"name\", \"render\", \"children\", \"as\", \"component\", \"className\"]);\n  var _useFormikContext = useFormikContext(),\n    formik = _objectWithoutPropertiesLoose(_useFormikContext, [\"validate\", \"validationSchema\"]);\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !!render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\\\"\" + name + \"\\\" render={({field, form}) => ...} /> with <Field name=\\\"\" + name + \"\\\">{({field, form, meta}) => ...}</Field>\") : invariant(false) : void 0;\n      !!(is && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.') : invariant(false) : void 0;\n      !!(component && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.') : invariant(false) : void 0;\n      !!(render && children && !isEmptyChildren(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored') : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  } // Register field and field-level validation with parent <Formik>\n\n  var registerField = formik.registerField,\n    unregisterField = formik.unregisterField;\n  useEffect(function () {\n    registerField(name, {\n      validate: validate\n    });\n    return function () {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  var field = formik.getFieldProps(_extends({\n    name: name\n  }, props));\n  var meta = formik.getFieldMeta(name);\n  var legacyBag = {\n    field: field,\n    form: formik\n  };\n  if (render) {\n    return render(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n  if (isFunction(children)) {\n    return children(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      var innerRef = props.innerRef,\n        rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n      return createElement(component, _extends({\n        ref: innerRef\n      }, field, rest, {\n        className: className\n      }), children);\n    } // We don't pass `meta` for backwards compat\n\n    return createElement(component, _extends({\n      field: field,\n      form: formik\n    }, props, {\n      className: className\n    }), children);\n  } // default to input here so we can check for both `as` and `children` above\n\n  var asElement = is || 'input';\n  if (typeof asElement === 'string') {\n    var _innerRef = props.innerRef,\n      _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n    return createElement(asElement, _extends({\n      ref: _innerRef\n    }, field, _rest, {\n      className: className\n    }), children);\n  }\n  return createElement(asElement, _extends({}, field, props, {\n    className: className\n  }), children);\n}\nvar Form = /*#__PURE__*/forwardRef(function (props, ref) {\n  // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n  // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n  var action = props.action,\n    rest = _objectWithoutPropertiesLoose(props, [\"action\"]);\n  var _action = action != null ? action : '#';\n  var _useFormikContext = useFormikContext(),\n    handleReset = _useFormikContext.handleReset,\n    handleSubmit = _useFormikContext.handleSubmit;\n  return createElement(\"form\", _extends({\n    onSubmit: handleSubmit,\n    ref: ref,\n    onReset: handleReset,\n    action: _action\n  }, rest));\n});\nForm.displayName = 'Form';\n\n/**\r\n * A public higher-order component to access the imperative API\r\n */\n\nfunction withFormik(_ref) {\n  var _ref$mapPropsToValues = _ref.mapPropsToValues,\n    mapPropsToValues = _ref$mapPropsToValues === void 0 ? function (vanillaProps) {\n      var val = {};\n      for (var k in vanillaProps) {\n        if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== 'function') {\n          // @todo TypeScript fix\n          val[k] = vanillaProps[k];\n        }\n      }\n      return val;\n    } : _ref$mapPropsToValues,\n    config = _objectWithoutPropertiesLoose(_ref, [\"mapPropsToValues\"]);\n  return function createFormik(Component$1) {\n    var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || 'Component';\n    /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */\n\n    var C = /*#__PURE__*/function (_React$Component) {\n      _inheritsLoose(C, _React$Component);\n      function C() {\n        var _this;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n        _this.validate = function (values) {\n          return config.validate(values, _this.props);\n        };\n        _this.validationSchema = function () {\n          return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n        };\n        _this.handleSubmit = function (values, actions) {\n          return config.handleSubmit(values, _extends({}, actions, {\n            props: _this.props\n          }));\n        };\n        _this.renderFormComponent = function (formikProps) {\n          return createElement(Component$1, _extends({}, _this.props, formikProps));\n        };\n        return _this;\n      }\n      var _proto = C.prototype;\n      _proto.render = function render() {\n        var _this$props = this.props,\n          props = _objectWithoutPropertiesLoose(_this$props, [\"children\"]);\n        return createElement(Formik, _extends({}, props, config, {\n          validate: config.validate && this.validate,\n          validationSchema: config.validationSchema && this.validationSchema,\n          initialValues: mapPropsToValues(this.props),\n          initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n          initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n          initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n          onSubmit: this.handleSubmit,\n          children: this.renderFormComponent\n        }));\n      };\n      return C;\n    }(Component);\n    C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n    return hoistNonReactStatics(C, Component$1 // cast type to ComponentClass (even if SFC)\n    );\n  };\n}\n\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */\n\nfunction connect(Comp) {\n  var C = function C(props) {\n    return createElement(FormikConsumer, null, function (formik) {\n      !!!formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : invariant(false) : void 0;\n      return createElement(Comp, _extends({}, props, {\n        formik: formik\n      }));\n    });\n  };\n  var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || 'Component'; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n\n  C.WrappedComponent = Comp;\n  C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n  return hoistNonReactStatics(C, Comp // cast type to ComponentClass (even if SFC)\n  );\n}\n\n/**\r\n * Some array helpers!\r\n */\n\nvar move = function move(array, from, to) {\n  var copy = copyArrayLike(array);\n  var value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n  var copy = copyArrayLike(arrayLike);\n  var a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [].concat(arrayLike);\n  } else {\n    var maxIndex = Object.keys(arrayLike).map(function (key) {\n      return parseInt(key);\n    }).reduce(function (max, el) {\n      return el > max ? el : max;\n    }, 0);\n    return Array.from(_extends({}, arrayLike, {\n      length: maxIndex + 1\n    }));\n  }\n};\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n  var fn = typeof alteration === 'function' ? alteration : defaultFunction;\n  return function (data) {\n    if (Array.isArray(data) || isObject(data)) {\n      var clone = copyArrayLike(data);\n      return fn(clone);\n    } // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n\n    return data;\n  };\n};\nvar FieldArrayInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FieldArrayInner, _React$Component);\n  function FieldArrayInner(props) {\n    var _this;\n    _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n\n    _this.updateArrayField = function (fn, alterTouched, alterErrors) {\n      var _this$props = _this.props,\n        name = _this$props.name,\n        setFormikState = _this$props.formik.setFormikState;\n      setFormikState(function (prevState) {\n        var updateErrors = createAlterationHandler(alterErrors, fn);\n        var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n        // otherwise it causes an error with unshift.\n\n        var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n        var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n        var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n        if (isEmptyArray(fieldError)) {\n          fieldError = undefined;\n        }\n        if (isEmptyArray(fieldTouched)) {\n          fieldTouched = undefined;\n        }\n        return _extends({}, prevState, {\n          values: values,\n          errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n          touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n        });\n      });\n    };\n    _this.push = function (value) {\n      return _this.updateArrayField(function (arrayLike) {\n        return [].concat(copyArrayLike(arrayLike), [cloneDeep(value)]);\n      }, false, false);\n    };\n    _this.handlePush = function (value) {\n      return function () {\n        return _this.push(value);\n      };\n    };\n    _this.swap = function (indexA, indexB) {\n      return _this.updateArrayField(function (array) {\n        return swap(array, indexA, indexB);\n      }, true, true);\n    };\n    _this.handleSwap = function (indexA, indexB) {\n      return function () {\n        return _this.swap(indexA, indexB);\n      };\n    };\n    _this.move = function (from, to) {\n      return _this.updateArrayField(function (array) {\n        return move(array, from, to);\n      }, true, true);\n    };\n    _this.handleMove = function (from, to) {\n      return function () {\n        return _this.move(from, to);\n      };\n    };\n    _this.insert = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return insert(array, index, value);\n      }, function (array) {\n        return insert(array, index, null);\n      }, function (array) {\n        return insert(array, index, null);\n      });\n    };\n    _this.handleInsert = function (index, value) {\n      return function () {\n        return _this.insert(index, value);\n      };\n    };\n    _this.replace = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return replace(array, index, value);\n      }, false, false);\n    };\n    _this.handleReplace = function (index, value) {\n      return function () {\n        return _this.replace(index, value);\n      };\n    };\n    _this.unshift = function (value) {\n      var length = -1;\n      _this.updateArrayField(function (array) {\n        var arr = array ? [value].concat(array) : [value];\n        length = arr.length;\n        return arr;\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      });\n      return length;\n    };\n    _this.handleUnshift = function (value) {\n      return function () {\n        return _this.unshift(value);\n      };\n    };\n    _this.handleRemove = function (index) {\n      return function () {\n        return _this.remove(index);\n      };\n    };\n    _this.handlePop = function () {\n      return function () {\n        return _this.pop();\n      };\n    };\n    _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n    _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n  var _proto = FieldArrayInner.prototype;\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.validateOnChange && this.props.formik.validateOnChange && !isEqual(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  };\n  _proto.remove = function remove(index) {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    var result;\n    this.updateArrayField(\n    // so this gets call 3 times\n    function (array) {\n      var copy = array ? copyArrayLike(array) : [];\n      if (!result) {\n        result = copy[index];\n      }\n      if (isFunction(copy.splice)) {\n        copy.splice(index, 1);\n      } // if the array only includes undefined values we have to return an empty array\n\n      return isFunction(copy.every) ? copy.every(function (v) {\n        return v === undefined;\n      }) ? [] : copy : copy;\n    }, true, true);\n    return result;\n  };\n  _proto.pop = function pop() {\n    // Remove relevant pieces of `touched` and `errors` too!\n    var result;\n    this.updateArrayField(\n    // so this gets call 3 times\n    function (array) {\n      var tmp = array.slice();\n      if (!result) {\n        result = tmp && tmp.pop && tmp.pop();\n      }\n      return tmp;\n    }, true, true);\n    return result;\n  };\n  _proto.render = function render() {\n    var arrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove\n    };\n    var _this$props2 = this.props,\n      component = _this$props2.component,\n      render = _this$props2.render,\n      children = _this$props2.children,\n      name = _this$props2.name,\n      _this$props2$formik = _this$props2.formik,\n      restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\"validate\", \"validationSchema\"]);\n    var props = _extends({}, arrayHelpers, {\n      form: restOfFormik,\n      name: name\n    });\n    return component ? createElement(component, props) : render ? render(props) : children // children come last, always called\n    ? typeof children === 'function' ? children(props) : !isEmptyChildren(children) ? Children.only(children) : null : null;\n  };\n  return FieldArrayInner;\n}(Component);\nFieldArrayInner.defaultProps = {\n  validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/connect(FieldArrayInner);\nvar ErrorMessageImpl = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorMessageImpl, _React$Component);\n  function ErrorMessageImpl() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = ErrorMessageImpl.prototype;\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      component = _this$props.component,\n      formik = _this$props.formik,\n      render = _this$props.render,\n      children = _this$props.children,\n      name = _this$props.name,\n      rest = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"formik\", \"render\", \"children\", \"name\"]);\n    var touch = getIn(formik.touched, name);\n    var error = getIn(formik.errors, name);\n    return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? createElement(component, rest, error) : error : null;\n  };\n  return ErrorMessageImpl;\n}(Component);\nvar ErrorMessage = /*#__PURE__*/connect(ErrorMessageImpl);\n\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */\n\nvar FastFieldInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FastFieldInner, _React$Component);\n  function FastFieldInner(props) {\n    var _this;\n    _this = _React$Component.call(this, props) || this;\n    var render = props.render,\n      children = props.children,\n      component = props.component,\n      is = props.as,\n      name = props.name;\n    !!render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : invariant(false) : void 0;\n    !!(component && render) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored') : invariant(false) : void 0;\n    !!(is && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.') : invariant(false) : void 0;\n    !!(component && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.') : invariant(false) : void 0;\n    !!(render && children && !isEmptyChildren(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored') : invariant(false) : void 0;\n    return _this;\n  }\n  var _proto = FastFieldInner.prototype;\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n  _proto.componentDidMount = function componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate\n    });\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      name = _this$props.name,\n      render = _this$props.render,\n      is = _this$props.as,\n      children = _this$props.children,\n      component = _this$props.component,\n      formik = _this$props.formik,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"validate\", \"name\", \"render\", \"as\", \"children\", \"component\", \"shouldUpdate\", \"formik\"]);\n    var restOfFormik = _objectWithoutPropertiesLoose(formik, [\"validate\", \"validationSchema\"]);\n    var field = formik.getFieldProps(_extends({\n      name: name\n    }, props));\n    var meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name)\n    };\n    var bag = {\n      field: field,\n      meta: meta,\n      form: restOfFormik\n    };\n    if (render) {\n      return render(bag);\n    }\n    if (isFunction(children)) {\n      return children(bag);\n    }\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        var innerRef = props.innerRef,\n          rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n        return createElement(component, _extends({\n          ref: innerRef\n        }, field, rest), children);\n      } // We don't pass `meta` for backwards compat\n\n      return createElement(component, _extends({\n        field: field,\n        form: formik\n      }, props), children);\n    } // default to input here so we can check for both `as` and `children` above\n\n    var asElement = is || 'input';\n    if (typeof asElement === 'string') {\n      var _innerRef = props.innerRef,\n        _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n      return createElement(asElement, _extends({\n        ref: _innerRef\n      }, field, _rest), children);\n    }\n    return createElement(asElement, _extends({}, field, props), children);\n  };\n  return FastFieldInner;\n}(Component);\nvar FastField = /*#__PURE__*/connect(FastFieldInner);\nexport { ErrorMessage, FastField, Field, FieldArray, Form, Formik, FormikConsumer, FormikContext, FormikProvider, connect, getActiveElement, getIn, insert, isEmptyArray, isEmptyChildren, isFunction, isInputEvent, isInteger, isNaN$1 as isNaN, isObject, isPromise, isString, move, prepareDataForValidation, replace, setIn, setNestedObjectValues, swap, useField, useFormik, useFormikContext, validateYupSchema, withFormik, yupToFormErrors };", "map": {"version": 3, "names": ["FormikContext", "createContext", "undefined", "displayName", "Formik<PERSON><PERSON><PERSON>", "Provider", "FormikConsumer", "Consumer", "useFormikContext", "formik", "useContext", "process", "env", "NODE_ENV", "invariant", "isEmptyArray", "value", "Array", "isArray", "length", "isFunction", "obj", "isObject", "isInteger", "String", "Math", "floor", "Number", "isString", "Object", "prototype", "toString", "call", "isNaN$1", "isNaN", "isEmptyChildren", "children", "Children", "count", "isPromise", "then", "isInputEvent", "target", "getActiveElement", "doc", "document", "activeElement", "body", "e", "getIn", "key", "def", "p", "path", "to<PERSON><PERSON>", "setIn", "res", "clone", "resVal", "i", "pathArray", "currentPath", "currentObj", "slice", "nextPath", "setNestedObjectValues", "object", "visited", "response", "WeakMap", "_i", "_Object$keys", "keys", "k", "val", "get", "set", "formikReducer", "state", "msg", "type", "_extends", "values", "payload", "touched", "isEqual", "errors", "status", "isSubmitting", "isValidating", "field", "submitCount", "emptyErrors", "emptyTouched", "useFormik", "_ref", "validateOnChange", "_ref$validateOnChange", "validateOnBlur", "_ref$validateOnBlur", "validateOnMount", "_ref$validateOnMount", "isInitialValid", "enableReinitialize", "_ref$enableReinitiali", "onSubmit", "rest", "_objectWithoutPropertiesLoose", "props", "initialValues", "useRef", "initialErrors", "initialTouched", "initialStatus", "isMounted", "fieldRegistry", "useEffect", "current", "useState", "setIteration", "_React$useState", "stateRef", "cloneDeep", "dispatch", "useCallback", "action", "prev", "x", "runValidateHandler", "Promise", "resolve", "reject", "maybePromisedErrors", "validate", "actualException", "console", "warn", "runValidationSchema", "validationSchema", "schema", "promise", "validateAt", "validateYupSchema", "err", "name", "yupToFormErrors", "runSingleFieldLevelValidation", "runFieldLevelValidations", "fieldKeysWithValidation", "filter", "f", "fieldValidations", "map", "all", "fieldErrorsList", "reduce", "curr", "index", "runAllValidations", "_ref2", "fieldErrors", "schemaErrors", "validateErrors", "combinedErrors", "deepmerge", "arrayMerge", "validateFormWithHighPriority", "useEventCallback", "resetForm", "nextState", "dispatchFn", "onReset", "maybePromisedOnReset", "imperativeMethods", "validateField", "<PERSON><PERSON><PERSON><PERSON>", "error", "registerField", "_ref3", "unregisterField", "setTouched", "shouldValidate", "willValidate", "setErrors", "set<PERSON><PERSON><PERSON>", "resolvedV<PERSON>ues", "setFieldError", "setFieldValue", "executeChange", "eventOrTextValue", "<PERSON><PERSON><PERSON>", "parsed", "persist", "currentTarget", "id", "checked", "outerHTML", "options", "multiple", "warnAboutMissingIdentifier", "htmlContent", "documentationAnchorLink", "handler<PERSON>ame", "test", "parseFloat", "getValueForCheckbox", "getSelectedValues", "handleChange", "eventOr<PERSON>ath", "event", "setFieldTouched", "executeBlur", "_e$target", "handleBlur", "eventOrString", "setFormikState", "stateOrCb", "setStatus", "setSubmitting", "submitForm", "isInstanceOfError", "Error", "isActuallyValid", "promiseOrUndefined", "executeSubmit", "result", "_errors", "handleSubmit", "preventDefault", "stopPropagation", "HTMLButtonElement", "attributes", "getNamedItem", "reason", "validateForm", "handleReset", "getFieldMeta", "initialValue", "initialError", "getFieldHelpers", "setValue", "setError", "getFieldProps", "nameOrOptions", "isAnObject", "valueState", "onChange", "onBlur", "valueProp", "is", "as", "indexOf", "dirty", "useMemo", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "<PERSON><PERSON>", "formik<PERSON>", "component", "render", "innerRef", "useImperativeHandle", "createElement", "only", "_ref4", "yupError", "inner", "message", "_iterator", "_isArray", "Symbol", "iterator", "_ref5", "next", "done", "sync", "context", "normalizedValues", "prepareDataForValidation", "abort<PERSON><PERSON><PERSON>", "data", "hasOwnProperty", "isPlainObject", "source", "destination", "for<PERSON>ach", "merge", "cloneRequested", "shouldClone", "isMergeableObject", "push", "from", "el", "selected", "currentValue", "Boolean", "currentArrayOfValues", "isValueInArray", "concat", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "fn", "ref", "_len", "arguments", "args", "_key", "apply", "useField", "props<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldName", "validateFn", "fieldHelpers", "Field", "className", "_useFormikContext", "meta", "legacyBag", "form", "asElement", "_innerRef", "_rest", "Form", "forwardRef", "_action", "withFormik", "mapPropsToValues", "_ref$mapPropsToValues", "vanillaProps", "config", "createFormik", "Component$1", "componentDisplayName", "constructor", "C", "_React$Component", "_this", "actions", "renderFormComponent", "formikProps", "_this$props", "mapPropsToStatus", "mapPropsToErrors", "mapPropsToTouched", "Component", "hoistNonReactStatics", "connect", "Comp", "WrappedComponent", "move", "array", "to", "copy", "copyArrayLike", "splice", "swap", "arrayLike", "indexA", "indexB", "a", "insert", "replace", "maxIndex", "parseInt", "max", "createAlterationHandler", "alteration", "defaultFunction", "FieldArrayInner", "updateArrayField", "alterTouched", "alterErrors", "prevState", "updateErrors", "updateTouched", "fieldError", "fieldTouched", "handlePush", "handleSwap", "handleMove", "handleInsert", "handleReplace", "unshift", "arr", "handleUnshift", "handleRemove", "remove", "handlePop", "pop", "bind", "_assertThisInitialized", "componentDidUpdate", "prevProps", "every", "v", "tmp", "arrayHelpers", "_this$props2", "restOfFormik", "_this$props2$formik", "defaultProps", "FieldArray", "ErrorMessageImpl", "shouldComponentUpdate", "touch", "ErrorMessage", "FastFieldInner", "shouldUpdate", "componentDidMount", "componentWillUnmount", "bag", "FastField"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\FormikContext.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\utils.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\Formik.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\Field.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\Form.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\withFormik.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\connect.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\FieldArray.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\ErrorMessage.tsx", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\node_modules\\formik\\src\\FastField.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { FormikContextType } from './types';\nimport invariant from 'tiny-warning';\n\nexport const FormikContext = React.createContext<FormikContextType<any>>(\n  undefined as any\n);\nFormikContext.displayName = 'FormikContext';\n\nexport const FormikProvider = FormikContext.Provider;\nexport const FormikConsumer = FormikContext.Consumer;\n\nexport function useFormikContext<Values>() {\n  const formik = React.useContext<FormikContextType<Values>>(FormikContext);\n\n  invariant(\n    !!formik,\n    `Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.`\n  );\n\n  return formik;\n}\n", "import clone from 'lodash/clone';\nimport toPath from 'lodash/toPath';\nimport * as React from 'react';\n\n// Assertions\n\n/** @private is the value an empty array? */\nexport const isEmptyArray = (value?: any) =>\n  Array.isArray(value) && value.length === 0;\n\n/** @private is the given object a Function? */\nexport const isFunction = (obj: any): obj is Function =>\n  typeof obj === 'function';\n\n/** @private is the given object an Object? */\nexport const isObject = (obj: any): obj is Object =>\n  obj !== null && typeof obj === 'object';\n\n/** @private is the given object an integer? */\nexport const isInteger = (obj: any): boolean =>\n  String(Math.floor(Number(obj))) === obj;\n\n/** @private is the given object a string? */\nexport const isString = (obj: any): obj is string =>\n  Object.prototype.toString.call(obj) === '[object String]';\n\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\nexport const isNaN = (obj: any): boolean => obj !== obj;\n\n/** @private Does a React component have exactly 0 children? */\nexport const isEmptyChildren = (children: any): boolean =>\n  React.Children.count(children) === 0;\n\n/** @private is the given object/value a promise? */\nexport const isPromise = (value: any): value is PromiseLike<any> =>\n  isObject(value) && isFunction(value.then);\n\n/** @private is the given object/value a type of synthetic event? */\nexport const isInputEvent = (value: any): value is React.SyntheticEvent<any> =>\n  value && isObject(value) && isObject(value.target);\n\n/**\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\n * not safe to call document.activeElement if there is nothing focused.\n *\n * The activeElement will be null only if the document or document body is not\n * yet defined.\n *\n * @param {?Document} doc Defaults to current document.\n * @return {Element | null}\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\n */\nexport function getActiveElement(doc?: Document): Element | null {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n\n/**\n * Deeply get a value from an object via its path.\n */\nexport function getIn(\n  obj: any,\n  key: string | string[],\n  def?: any,\n  p: number = 0\n) {\n  const path = toPath(key);\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  }\n\n  // check if path is not in the end\n  if (p !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n\n/**\n * Deeply set a value from in object via it's path. If the value at `path`\n * has changed, return a shallow copy of obj with `value` set at `path`.\n * If `value` has not changed, return the original `obj`.\n *\n * Existing objects / arrays along `path` are also shallow copied. Sibling\n * objects along path retain the same internal js reference. Since new\n * objects / arrays are only created along `path`, we can test if anything\n * changed in a nested structure by comparing the object's reference in\n * the old and new object, similar to how russian doll cache invalidation\n * works.\n *\n * In earlier versions of this function, which used cloneDeep, there were\n * issues whereby settings a nested value would mutate the parent\n * instead of creating a new object. `clone` avoids that bug making a\n * shallow copy of the objects along the update path\n * so no object is mutated in place.\n *\n * Before changing this function, please read through the following\n * discussions.\n *\n * @see https://github.com/developit/linkstate\n * @see https://github.com/jaredpalmer/formik/pull/123\n */\nexport function setIn(obj: any, path: string, value: any): any {\n  let res: any = clone(obj); // this keeps inheritance when obj is a class\n  let resVal: any = res;\n  let i = 0;\n  let pathArray = toPath(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    const currentPath: string = pathArray[i];\n    let currentObj: any = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = clone(currentObj);\n    } else {\n      const nextPath: string = pathArray[i + 1];\n      resVal = resVal[currentPath] =\n        isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  }\n\n  // Return original object if new value is the same as current\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  }\n\n  // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n\n/**\n * Recursively a set the same value for all keys and arrays nested object, cloning\n * @param object\n * @param value\n * @param visited\n * @param response\n */\nexport function setNestedObjectValues<T>(\n  object: any,\n  value: any,\n  visited: any = new WeakMap(),\n  response: any = {}\n): T {\n  for (let k of Object.keys(object)) {\n    const val = object[k];\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true);\n        // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n\n  return response;\n}\n", "import deepmerge from 'deepmerge';\nimport isPlainObject from 'lodash/isPlainObject';\nimport cloneDeep from 'lodash/cloneDeep';\nimport * as React from 'react';\nimport isEqual from 'react-fast-compare';\nimport invariant from 'tiny-warning';\nimport { FieldConfig } from './Field';\nimport { FormikProvider } from './FormikContext';\nimport {\n  FieldHelperProps,\n  FieldInputProps,\n  FieldMetaProps,\n  FormikConfig,\n  FormikErrors,\n  FormikHandlers,\n  FormikHelpers,\n  FormikProps,\n  FormikState,\n  FormikTouched,\n  FormikValues,\n} from './types';\nimport {\n  getActiveElement,\n  getIn,\n  isEmptyChildren,\n  isFunction,\n  isObject,\n  isPromise,\n  isString,\n  setIn,\n  setNestedObjectValues,\n} from './utils';\n\ntype FormikMessage<Values> =\n  | { type: 'SUBMIT_ATTEMPT' }\n  | { type: 'SUBMIT_FAILURE' }\n  | { type: 'SUBMIT_SUCCESS' }\n  | { type: 'SET_ISVALIDATING'; payload: boolean }\n  | { type: 'SET_ISSUBMITTING'; payload: boolean }\n  | { type: 'SET_VALUES'; payload: Values }\n  | { type: 'SET_FIELD_VALUE'; payload: { field: string; value?: any } }\n  | { type: 'SET_FIELD_TOUCHED'; payload: { field: string; value?: boolean } }\n  | { type: 'SET_FIELD_ERROR'; payload: { field: string; value?: string } }\n  | { type: 'SET_TOUCHED'; payload: FormikTouched<Values> }\n  | { type: 'SET_ERRORS'; payload: FormikErrors<Values> }\n  | { type: 'SET_STATUS'; payload: any }\n  | {\n      type: 'SET_FORMIK_STATE';\n      payload: (s: FormikState<Values>) => FormikState<Values>;\n    }\n  | {\n      type: 'RESET_FORM';\n      payload: FormikState<Values>;\n    };\n\n// State reducer\nfunction formikReducer<Values>(\n  state: FormikState<Values>,\n  msg: FormikMessage<Values>\n) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return { ...state, values: msg.payload };\n    case 'SET_TOUCHED':\n      return { ...state, touched: msg.payload };\n    case 'SET_ERRORS':\n      if (isEqual(state.errors, msg.payload)) {\n        return state;\n      }\n\n      return { ...state, errors: msg.payload };\n    case 'SET_STATUS':\n      return { ...state, status: msg.payload };\n    case 'SET_ISSUBMITTING':\n      return { ...state, isSubmitting: msg.payload };\n    case 'SET_ISVALIDATING':\n      return { ...state, isValidating: msg.payload };\n    case 'SET_FIELD_VALUE':\n      return {\n        ...state,\n        values: setIn(state.values, msg.payload.field, msg.payload.value),\n      };\n    case 'SET_FIELD_TOUCHED':\n      return {\n        ...state,\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value),\n      };\n    case 'SET_FIELD_ERROR':\n      return {\n        ...state,\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value),\n      };\n    case 'RESET_FORM':\n      return { ...state, ...msg.payload };\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n    case 'SUBMIT_ATTEMPT':\n      return {\n        ...state,\n        touched: setNestedObjectValues<FormikTouched<Values>>(\n          state.values,\n          true\n        ),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1,\n      };\n    case 'SUBMIT_FAILURE':\n      return {\n        ...state,\n        isSubmitting: false,\n      };\n    case 'SUBMIT_SUCCESS':\n      return {\n        ...state,\n        isSubmitting: false,\n      };\n    default:\n      return state;\n  }\n}\n\n// Initial empty states // objects\nconst emptyErrors: FormikErrors<unknown> = {};\nconst emptyTouched: FormikTouched<unknown> = {};\n\n// This is an object that contains a map of all registered fields\n// and their validate functions\ninterface FieldRegistry {\n  [field: string]: {\n    validate: (value: any) => string | Promise<string> | undefined;\n  };\n}\n\nexport function useFormik<Values extends FormikValues = FormikValues>({\n  validateOnChange = true,\n  validateOnBlur = true,\n  validateOnMount = false,\n  isInitialValid,\n  enableReinitialize = false,\n  onSubmit,\n  ...rest\n}: FormikConfig<Values>) {\n  const props = {\n    validateOnChange,\n    validateOnBlur,\n    validateOnMount,\n    onSubmit,\n    ...rest,\n  };\n  const initialValues = React.useRef(props.initialValues);\n  const initialErrors = React.useRef(props.initialErrors || emptyErrors);\n  const initialTouched = React.useRef(props.initialTouched || emptyTouched);\n  const initialStatus = React.useRef(props.initialStatus);\n  const isMounted = React.useRef<boolean>(false);\n  const fieldRegistry = React.useRef<FieldRegistry>({});\n  if (__DEV__) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      invariant(\n        typeof isInitialValid === 'undefined',\n        'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.'\n      );\n      // eslint-disable-next-line\n    }, []);\n  }\n\n  React.useEffect(() => {\n    isMounted.current = true;\n\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  const [, setIteration] = React.useState(0);\n  const stateRef = React.useRef<FormikState<Values>>({\n    values: cloneDeep(props.initialValues),\n    errors: cloneDeep(props.initialErrors) || emptyErrors,\n    touched: cloneDeep(props.initialTouched) || emptyTouched,\n    status: cloneDeep(props.initialStatus),\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0,\n  });\n\n  const state = stateRef.current;\n\n  const dispatch = React.useCallback((action: FormikMessage<Values>) => {\n    const prev = stateRef.current;\n\n    stateRef.current = formikReducer(prev, action);\n\n    // force rerender\n    if (prev !== stateRef.current) setIteration(x => x + 1);\n  }, []);\n\n  const runValidateHandler = React.useCallback(\n    (values: Values, field?: string): Promise<FormikErrors<Values>> => {\n      return new Promise((resolve, reject) => {\n        const maybePromisedErrors = (props.validate as any)(values, field);\n        if (maybePromisedErrors == null) {\n          // use loose null check here on purpose\n          resolve(emptyErrors);\n        } else if (isPromise(maybePromisedErrors)) {\n          (maybePromisedErrors as Promise<any>).then(\n            errors => {\n              resolve(errors || emptyErrors);\n            },\n            actualException => {\n              if (process.env.NODE_ENV !== 'production') {\n                console.warn(\n                  `Warning: An unhandled error was caught during validation in <Formik validate />`,\n                  actualException\n                );\n              }\n\n              reject(actualException);\n            }\n          );\n        } else {\n          resolve(maybePromisedErrors);\n        }\n      });\n    },\n    [props.validate]\n  );\n\n  /**\n   * Run validation against a Yup schema and optionally run a function if successful\n   */\n  const runValidationSchema = React.useCallback(\n    (values: Values, field?: string): Promise<FormikErrors<Values>> => {\n      const validationSchema = props.validationSchema;\n      const schema = isFunction(validationSchema)\n        ? validationSchema(field)\n        : validationSchema;\n      const promise =\n        field && schema.validateAt\n          ? schema.validateAt(field, values)\n          : validateYupSchema(values, schema);\n      return new Promise((resolve, reject) => {\n        promise.then(\n          () => {\n            resolve(emptyErrors);\n          },\n          (err: any) => {\n            // Yup will throw a validation error if validation fails. We catch those and\n            // resolve them into Formik errors. We can sniff if something is a Yup error\n            // by checking error.name.\n            // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n            if (err.name === 'ValidationError') {\n              resolve(yupToFormErrors(err));\n            } else {\n              // We throw any other errors\n              if (process.env.NODE_ENV !== 'production') {\n                console.warn(\n                  `Warning: An unhandled error was caught during validation in <Formik validationSchema />`,\n                  err\n                );\n              }\n\n              reject(err);\n            }\n          }\n        );\n      });\n    },\n    [props.validationSchema]\n  );\n\n  const runSingleFieldLevelValidation = React.useCallback(\n    (field: string, value: void | string): Promise<string> => {\n      return new Promise(resolve =>\n        resolve(fieldRegistry.current[field].validate(value) as string)\n      );\n    },\n    []\n  );\n\n  const runFieldLevelValidations = React.useCallback(\n    (values: Values): Promise<FormikErrors<Values>> => {\n      const fieldKeysWithValidation: string[] = Object.keys(\n        fieldRegistry.current\n      ).filter(f => isFunction(fieldRegistry.current[f].validate));\n\n      // Construct an array with all of the field validation functions\n      const fieldValidations: Promise<string>[] =\n        fieldKeysWithValidation.length > 0\n          ? fieldKeysWithValidation.map(f =>\n              runSingleFieldLevelValidation(f, getIn(values, f))\n            )\n          : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n      return Promise.all(fieldValidations).then((fieldErrorsList: string[]) =>\n        fieldErrorsList.reduce((prev, curr, index) => {\n          if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n            return prev;\n          }\n          if (curr) {\n            prev = setIn(prev, fieldKeysWithValidation[index], curr);\n          }\n          return prev;\n        }, {})\n      );\n    },\n    [runSingleFieldLevelValidation]\n  );\n\n  // Run all validations and return the result\n  const runAllValidations = React.useCallback(\n    (values: Values) => {\n      return Promise.all([\n        runFieldLevelValidations(values),\n        props.validationSchema ? runValidationSchema(values) : {},\n        props.validate ? runValidateHandler(values) : {},\n      ]).then(([fieldErrors, schemaErrors, validateErrors]) => {\n        const combinedErrors = deepmerge.all<FormikErrors<Values>>(\n          [fieldErrors, schemaErrors, validateErrors],\n          { arrayMerge }\n        );\n        return combinedErrors;\n      });\n    },\n    [\n      props.validate,\n      props.validationSchema,\n      runFieldLevelValidations,\n      runValidateHandler,\n      runValidationSchema,\n    ]\n  );\n\n  // Run all validations methods and update state accordingly\n  const validateFormWithHighPriority = useEventCallback(\n    (values: Values = state.values) => {\n      dispatch({ type: 'SET_ISVALIDATING', payload: true });\n      return runAllValidations(values).then(combinedErrors => {\n        if (!!isMounted.current) {\n          dispatch({ type: 'SET_ISVALIDATING', payload: false });\n          dispatch({ type: 'SET_ERRORS', payload: combinedErrors });\n        }\n        return combinedErrors;\n      });\n    }\n  );\n\n  React.useEffect(() => {\n    if (\n      validateOnMount &&\n      isMounted.current === true &&\n      isEqual(initialValues.current, props.initialValues)\n    ) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n\n  const resetForm = React.useCallback(\n    (nextState?: Partial<FormikState<Values>>) => {\n      const values =\n        nextState && nextState.values\n          ? nextState.values\n          : initialValues.current;\n      const errors =\n        nextState && nextState.errors\n          ? nextState.errors\n          : initialErrors.current\n          ? initialErrors.current\n          : props.initialErrors || {};\n      const touched =\n        nextState && nextState.touched\n          ? nextState.touched\n          : initialTouched.current\n          ? initialTouched.current\n          : props.initialTouched || {};\n      const status =\n        nextState && nextState.status\n          ? nextState.status\n          : initialStatus.current\n          ? initialStatus.current\n          : props.initialStatus;\n      initialValues.current = values;\n      initialErrors.current = errors;\n      initialTouched.current = touched;\n      initialStatus.current = status;\n\n      const dispatchFn = () => {\n        dispatch({\n          type: 'RESET_FORM',\n          payload: {\n            isSubmitting: !!nextState && !!nextState.isSubmitting,\n            errors,\n            touched,\n            status,\n            values,\n            isValidating: !!nextState && !!nextState.isValidating,\n            submitCount:\n              !!nextState &&\n              !!nextState.submitCount &&\n              typeof nextState.submitCount === 'number'\n                ? nextState.submitCount\n                : 0,\n          },\n        });\n      };\n\n      if (props.onReset) {\n        const maybePromisedOnReset = (props.onReset as any)(\n          state.values,\n          imperativeMethods\n        );\n\n        if (isPromise(maybePromisedOnReset)) {\n          (maybePromisedOnReset as Promise<any>).then(dispatchFn);\n        } else {\n          dispatchFn();\n        }\n      } else {\n        dispatchFn();\n      }\n    },\n    [props.initialErrors, props.initialStatus, props.initialTouched, props.onReset]\n  );\n\n  React.useEffect(() => {\n    if (\n      isMounted.current === true &&\n      !isEqual(initialValues.current, props.initialValues)\n    ) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [\n    enableReinitialize,\n    props.initialValues,\n    resetForm,\n    validateOnMount,\n    validateFormWithHighPriority,\n  ]);\n\n  React.useEffect(() => {\n    if (\n      enableReinitialize &&\n      isMounted.current === true &&\n      !isEqual(initialErrors.current, props.initialErrors)\n    ) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors,\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n\n  React.useEffect(() => {\n    if (\n      enableReinitialize &&\n      isMounted.current === true &&\n      !isEqual(initialTouched.current, props.initialTouched)\n    ) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched,\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n\n  React.useEffect(() => {\n    if (\n      enableReinitialize &&\n      isMounted.current === true &&\n      !isEqual(initialStatus.current, props.initialStatus)\n    ) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus,\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n\n  const validateField = useEventCallback((name: string) => {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n\n    if (\n      fieldRegistry.current[name] &&\n      isFunction(fieldRegistry.current[name].validate)\n    ) {\n      const value = getIn(state.values, name);\n      const maybePromise = fieldRegistry.current[name].validate(value);\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({ type: 'SET_ISVALIDATING', payload: true });\n        return maybePromise\n          .then((x: any) => x)\n          .then((error: string) => {\n            dispatch({\n              type: 'SET_FIELD_ERROR',\n              payload: { field: name, value: error },\n            });\n            dispatch({ type: 'SET_ISVALIDATING', payload: false });\n          });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise as string | undefined,\n          },\n        });\n        return Promise.resolve(maybePromise as string | undefined);\n      }\n    } else if (props.validationSchema) {\n      dispatch({ type: 'SET_ISVALIDATING', payload: true });\n      return runValidationSchema(state.values, name)\n        .then((x: any) => x)\n        .then((error: any) => {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: { field: name, value: getIn(error, name) },\n          });\n          dispatch({ type: 'SET_ISVALIDATING', payload: false });\n        });\n    }\n\n    return Promise.resolve();\n  });\n\n  const registerField = React.useCallback((name: string, { validate }: any) => {\n    fieldRegistry.current[name] = {\n      validate,\n    };\n  }, []);\n\n  const unregisterField = React.useCallback((name: string) => {\n    delete fieldRegistry.current[name];\n  }, []);\n\n  const setTouched = useEventCallback(\n    (touched: FormikTouched<Values>, shouldValidate?: boolean) => {\n      dispatch({ type: 'SET_TOUCHED', payload: touched });\n      const willValidate =\n        shouldValidate === undefined ? validateOnBlur : shouldValidate;\n      return willValidate\n        ? validateFormWithHighPriority(state.values)\n        : Promise.resolve();\n    }\n  );\n\n  const setErrors = React.useCallback((errors: FormikErrors<Values>) => {\n    dispatch({ type: 'SET_ERRORS', payload: errors });\n  }, []);\n\n  const setValues = useEventCallback(\n    (values: React.SetStateAction<Values>, shouldValidate?: boolean) => {\n      const resolvedValues = isFunction(values) ? values(state.values) : values;\n\n      dispatch({ type: 'SET_VALUES', payload: resolvedValues });\n      const willValidate =\n        shouldValidate === undefined ? validateOnChange : shouldValidate;\n      return willValidate\n        ? validateFormWithHighPriority(resolvedValues)\n        : Promise.resolve();\n    }\n  );\n\n  const setFieldError = React.useCallback(\n    (field: string, value: string | undefined) => {\n      dispatch({\n        type: 'SET_FIELD_ERROR',\n        payload: { field, value },\n      });\n    },\n    []\n  );\n\n  const setFieldValue = useEventCallback(\n    (field: string, value: any, shouldValidate?: boolean) => {\n      dispatch({\n        type: 'SET_FIELD_VALUE',\n        payload: {\n          field,\n          value,\n        },\n      });\n      const willValidate =\n        shouldValidate === undefined ? validateOnChange : shouldValidate;\n      return willValidate\n        ? validateFormWithHighPriority(setIn(state.values, field, value))\n        : Promise.resolve();\n    }\n  );\n\n  const executeChange = React.useCallback(\n    (eventOrTextValue: string | React.ChangeEvent<any>, maybePath?: string) => {\n      // By default, assume that the first argument is a string. This allows us to use\n      // handleChange with React Native and React Native Web's onChangeText prop which\n      // provides just the value of the input.\n      let field = maybePath;\n      let val = eventOrTextValue;\n      let parsed;\n      // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n      // so we handle like we would a normal HTML change event.\n      if (!isString(eventOrTextValue)) {\n        // If we can, persist the event\n        // @see https://reactjs.org/docs/events.html#event-pooling\n        if ((eventOrTextValue as any).persist) {\n          (eventOrTextValue as React.ChangeEvent<any>).persist();\n        }\n        const target = eventOrTextValue.target\n          ? (eventOrTextValue as React.ChangeEvent<any>).target\n          : (eventOrTextValue as React.ChangeEvent<any>).currentTarget;\n\n        const {\n          type,\n          name,\n          id,\n          value,\n          checked,\n          outerHTML,\n          options,\n          multiple,\n        } = target;\n\n        field = maybePath ? maybePath : name ? name : id;\n        if (!field && __DEV__) {\n          warnAboutMissingIdentifier({\n            htmlContent: outerHTML,\n            documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n            handlerName: 'handleChange',\n          });\n        }\n        val = /number|range/.test(type)\n          ? ((parsed = parseFloat(value)), isNaN(parsed) ? '' : parsed)\n          : /checkbox/.test(type) // checkboxes\n          ? getValueForCheckbox(getIn(state.values, field!), checked, value)\n          : options && multiple // <select multiple>\n          ? getSelectedValues(options)\n          : value;\n      }\n\n      if (field) {\n        // Set form fields by name\n        setFieldValue(field, val);\n      }\n    },\n    [setFieldValue, state.values]\n  );\n\n  const handleChange = useEventCallback<FormikHandlers['handleChange']>(\n    (\n      eventOrPath: string | React.ChangeEvent<any>\n    ): void | ((eventOrTextValue: string | React.ChangeEvent<any>) => void) => {\n      if (isString(eventOrPath)) {\n        return event => executeChange(event, eventOrPath);\n      } else {\n        executeChange(eventOrPath);\n      }\n    }\n  );\n\n  const setFieldTouched = useEventCallback(\n    (field: string, touched: boolean = true, shouldValidate?: boolean) => {\n      dispatch({\n        type: 'SET_FIELD_TOUCHED',\n        payload: {\n          field,\n          value: touched,\n        },\n      });\n      const willValidate =\n        shouldValidate === undefined ? validateOnBlur : shouldValidate;\n      return willValidate\n        ? validateFormWithHighPriority(state.values)\n        : Promise.resolve();\n    }\n  );\n\n  const executeBlur = React.useCallback(\n    (e: any, path?: string) => {\n      if (e.persist) {\n        e.persist();\n      }\n      const { name, id, outerHTML } = e.target;\n      const field = path ? path : name ? name : id;\n\n      if (!field && __DEV__) {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handleblur-e-any--void',\n          handlerName: 'handleBlur',\n        });\n      }\n\n      setFieldTouched(field, true);\n    },\n    [setFieldTouched]\n  );\n\n  const handleBlur = useEventCallback<FormikHandlers['handleBlur']>(\n    (eventOrString: any): void | ((e: any) => void) => {\n      if (isString(eventOrString)) {\n        return event => executeBlur(event, eventOrString);\n      } else {\n        executeBlur(eventOrString);\n      }\n    }\n  );\n\n  const setFormikState = React.useCallback(\n    (\n      stateOrCb:\n        | FormikState<Values>\n        | ((state: FormikState<Values>) => FormikState<Values>)\n    ): void => {\n      if (isFunction(stateOrCb)) {\n        dispatch({ type: 'SET_FORMIK_STATE', payload: stateOrCb });\n      } else {\n        dispatch({ type: 'SET_FORMIK_STATE', payload: () => stateOrCb });\n      }\n    },\n    []\n  );\n\n  const setStatus = React.useCallback((status: any) => {\n    dispatch({ type: 'SET_STATUS', payload: status });\n  }, []);\n\n  const setSubmitting = React.useCallback((isSubmitting: boolean) => {\n    dispatch({ type: 'SET_ISSUBMITTING', payload: isSubmitting });\n  }, []);\n\n  const submitForm = useEventCallback(() => {\n    dispatch({ type: 'SUBMIT_ATTEMPT' });\n    return validateFormWithHighPriority().then(\n      (combinedErrors: FormikErrors<Values>) => {\n        // In case an error was thrown and passed to the resolved Promise,\n        // `combinedErrors` can be an instance of an Error. We need to check\n        // that and abort the submit.\n        // If we don't do that, calling `Object.keys(new Error())` yields an\n        // empty array, which causes the validation to pass and the form\n        // to be submitted.\n\n        const isInstanceOfError = combinedErrors instanceof Error;\n        const isActuallyValid =\n          !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n        if (isActuallyValid) {\n          // Proceed with submit...\n          //\n          // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n          // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n          // This would be fine in simple cases, but make it impossible to disable submit\n          // buttons where people use callbacks or promises as side effects (which is basically\n          // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n          //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n          // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n          // cleanup of isSubmitting on behalf of the consumer.\n          let promiseOrUndefined;\n          try {\n            promiseOrUndefined = executeSubmit();\n            // Bail if it's sync, consumer is responsible for cleaning up\n            // via setSubmitting(false)\n            if (promiseOrUndefined === undefined) {\n              return;\n            }\n          } catch (error) {\n            throw error;\n          }\n\n          return Promise.resolve(promiseOrUndefined)\n            .then(result => {\n              if (!!isMounted.current) {\n                dispatch({ type: 'SUBMIT_SUCCESS' });\n              }\n              return result;\n            })\n            .catch(_errors => {\n              if (!!isMounted.current) {\n                dispatch({ type: 'SUBMIT_FAILURE' });\n                // This is a legit error rejected by the onSubmit fn\n                // so we don't want to break the promise chain\n                throw _errors;\n              }\n            });\n        } else if (!!isMounted.current) {\n          // ^^^ Make sure Formik is still mounted before updating state\n          dispatch({ type: 'SUBMIT_FAILURE' });\n          // throw combinedErrors;\n          if (isInstanceOfError) {\n            throw combinedErrors;\n          }\n        }\n        return;\n      }\n    );\n  });\n\n  const handleSubmit = useEventCallback(\n    (e?: React.FormEvent<HTMLFormElement>) => {\n      if (e && e.preventDefault && isFunction(e.preventDefault)) {\n        e.preventDefault();\n      }\n\n      if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n        e.stopPropagation();\n      }\n\n      // Warn if form submission is triggered by a <button> without a\n      // specified `type` attribute during development. This mitigates\n      // a common gotcha in forms with both reset and submit buttons,\n      // where the dev forgets to add type=\"button\" to the reset button.\n      if (__DEV__ && typeof document !== 'undefined') {\n        // Safely get the active element (works with IE)\n        const activeElement = getActiveElement();\n        if (\n          activeElement !== null &&\n          activeElement instanceof HTMLButtonElement\n        ) {\n          invariant(\n            activeElement.attributes &&\n              activeElement.attributes.getNamedItem('type'),\n            'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.'\n          );\n        }\n      }\n\n      submitForm().catch(reason => {\n        console.warn(\n          `Warning: An unhandled error was caught from submitForm()`,\n          reason\n        );\n      });\n    }\n  );\n\n  const imperativeMethods: FormikHelpers<Values> = {\n    resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField,\n    setErrors,\n    setFieldError,\n    setFieldTouched,\n    setFieldValue,\n    setStatus,\n    setSubmitting,\n    setTouched,\n    setValues,\n    setFormikState,\n    submitForm,\n  };\n\n  const executeSubmit = useEventCallback(() => {\n    return onSubmit(state.values, imperativeMethods);\n  });\n\n  const handleReset = useEventCallback(e => {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n\n    resetForm();\n  });\n\n  const getFieldMeta = React.useCallback(\n    (name: string): FieldMetaProps<any> => {\n      return {\n        value: getIn(state.values, name),\n        error: getIn(state.errors, name),\n        touched: !!getIn(state.touched, name),\n        initialValue: getIn(initialValues.current, name),\n        initialTouched: !!getIn(initialTouched.current, name),\n        initialError: getIn(initialErrors.current, name),\n      };\n    },\n    [state.errors, state.touched, state.values]\n  );\n\n  const getFieldHelpers = React.useCallback(\n    (name: string): FieldHelperProps<any> => {\n      return {\n        setValue: (value: any, shouldValidate?: boolean) =>\n          setFieldValue(name, value, shouldValidate),\n        setTouched: (value: boolean, shouldValidate?: boolean) =>\n          setFieldTouched(name, value, shouldValidate),\n        setError: (value: any) => setFieldError(name, value),\n      };\n    },\n    [setFieldValue, setFieldTouched, setFieldError]\n  );\n\n  const getFieldProps = React.useCallback(\n    (nameOrOptions: string | FieldConfig<any>): FieldInputProps<any> => {\n      const isAnObject = isObject(nameOrOptions);\n      const name = isAnObject\n        ? (nameOrOptions as FieldConfig<any>).name\n        : nameOrOptions;\n      const valueState = getIn(state.values, name);\n\n      const field: FieldInputProps<any> = {\n        name,\n        value: valueState,\n        onChange: handleChange,\n        onBlur: handleBlur,\n      };\n      if (isAnObject) {\n        const {\n          type,\n          value: valueProp, // value is special for checkboxes\n          as: is,\n          multiple,\n        } = nameOrOptions as FieldConfig<any>;\n\n        if (type === 'checkbox') {\n          if (valueProp === undefined) {\n            field.checked = !!valueState;\n          } else {\n            field.checked = !!(\n              Array.isArray(valueState) && ~valueState.indexOf(valueProp)\n            );\n            field.value = valueProp;\n          }\n        } else if (type === 'radio') {\n          field.checked = valueState === valueProp;\n          field.value = valueProp;\n        } else if (is === 'select' && multiple) {\n          field.value = field.value || [];\n          field.multiple = true;\n        }\n      }\n      return field;\n    },\n    [handleBlur, handleChange, state.values]\n  );\n\n  const dirty = React.useMemo(\n    () => !isEqual(initialValues.current, state.values),\n    [initialValues.current, state.values]\n  );\n\n  const isValid = React.useMemo(\n    () =>\n      typeof isInitialValid !== 'undefined'\n        ? dirty\n          ? state.errors && Object.keys(state.errors).length === 0\n          : isInitialValid !== false && isFunction(isInitialValid)\n          ? (isInitialValid as (props: FormikConfig<Values>) => boolean)(props)\n          : (isInitialValid as boolean)\n        : state.errors && Object.keys(state.errors).length === 0,\n    [isInitialValid, dirty, state.errors, props]\n  );\n\n  const ctx = {\n    ...state,\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur,\n    handleChange,\n    handleReset,\n    handleSubmit,\n    resetForm,\n    setErrors,\n    setFormikState,\n    setFieldTouched,\n    setFieldValue,\n    setFieldError,\n    setStatus,\n    setSubmitting,\n    setTouched,\n    setValues,\n    submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField,\n    isValid,\n    dirty,\n    unregisterField,\n    registerField,\n    getFieldProps,\n    getFieldMeta,\n    getFieldHelpers,\n    validateOnBlur,\n    validateOnChange,\n    validateOnMount,\n  };\n\n  return ctx;\n}\n\nexport function Formik<\n  Values extends FormikValues = FormikValues,\n  ExtraProps = {}\n>(props: FormikConfig<Values> & ExtraProps) {\n  const formikbag = useFormik<Values>(props);\n  const { component, children, render, innerRef } = props;\n\n  // This allows folks to pass a ref to <Formik />\n  React.useImperativeHandle(innerRef, () => formikbag);\n\n  if (__DEV__) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      invariant(\n        !props.render,\n        `<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>`\n      );\n      // eslint-disable-next-line\n    }, []);\n  }\n  return (\n    <FormikProvider value={formikbag}>\n      {component\n        ? React.createElement(component as any, formikbag)\n        : render\n        ? render(formikbag)\n        : children // children come last, always called\n        ? isFunction(children)\n          ? (children as (bag: FormikProps<Values>) => React.ReactNode)(\n              formikbag as FormikProps<Values>\n            )\n          : !isEmptyChildren(children)\n          ? React.Children.only(children)\n          : null\n        : null}\n    </FormikProvider>\n  );\n}\n\nfunction warnAboutMissingIdentifier({\n  htmlContent,\n  documentationAnchorLink,\n  handlerName,\n}: {\n  htmlContent: string;\n  documentationAnchorLink: string;\n  handlerName: string;\n}) {\n  console.warn(\n    `Warning: Formik called \\`${handlerName}\\`, but you forgot to pass an \\`id\\` or \\`name\\` attribute to your input:\n    ${htmlContent}\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#${documentationAnchorLink}\n  `\n  );\n}\n\n/**\n * Transform Yup ValidationError to a more usable object\n */\nexport function yupToFormErrors<Values>(yupError: any): FormikErrors<Values> {\n  let errors: FormikErrors<Values> = {};\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n    for (let err of yupError.inner) {\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n  return errors;\n}\n\n/**\n * Validate a yup schema.\n */\nexport function validateYupSchema<T extends FormikValues>(\n  values: T,\n  schema: any,\n  sync: boolean = false,\n  context?: any\n): Promise<Partial<T>> {\n  const normalizedValues: FormikValues = prepareDataForValidation(values);\n\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues,\n  });\n}\n\n/**\n * Recursively prepare values.\n */\nexport function prepareDataForValidation<T extends FormikValues>(\n  values: T\n): FormikValues {\n  let data: FormikValues = Array.isArray(values) ? [] : {};\n  for (let k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      const key = String(k);\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map((value: any) => {\n          if (Array.isArray(value) === true || isPlainObject(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if (isPlainObject(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n  return data;\n}\n\n/**\n * deepmerge array merging algorithm\n * https://github.com/KyleAMathews/deepmerge#combine-array\n */\nfunction arrayMerge(target: any[], source: any[], options: any): any[] {\n  const destination = target.slice();\n\n  source.forEach(function merge(e: any, i: number) {\n    if (typeof destination[i] === 'undefined') {\n      const cloneRequested = options.clone !== false;\n      const shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone\n        ? deepmerge(Array.isArray(e) ? [] : {}, e, options)\n        : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = deepmerge(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n\n/** Return multi select values based on an array of options */\nfunction getSelectedValues(options: any[]) {\n  return Array.from(options)\n    .filter(el => el.selected)\n    .map(el => el.value);\n}\n\n/** Return the next value for a checkbox */\nfunction getValueForCheckbox(\n  currentValue: string | any[],\n  checked: boolean,\n  valueProp: any\n) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  }\n\n  // If the currentValue was not a boolean we want to return an array\n  let currentArrayOfValues = [];\n  let isValueInArray = false;\n  let index = -1;\n\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  }\n\n  // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  }\n\n  // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  }\n\n  // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n  return currentArrayOfValues\n    .slice(0, index)\n    .concat(currentArrayOfValues.slice(index + 1));\n}\n\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\nconst useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined'\n    ? React.useLayoutEffect\n    : React.useEffect;\n\nfunction useEventCallback<T extends (...args: any[]) => any>(fn: T): T {\n  const ref: any = React.useRef(fn);\n\n  // we copy a ref to the callback scoped to the current state/props on each render\n  useIsomorphicLayoutEffect(() => {\n    ref.current = fn;\n  });\n\n  return React.useCallback(\n    (...args: any[]) => ref.current.apply(void 0, args),\n    []\n  ) as T;\n}\n", "import * as React from 'react';\nimport {\n  FormikProps,\n  GenericFieldHTMLAttributes,\n  FieldMetaProps,\n  FieldHelperProps,\n  FieldInputProps,\n  FieldValidator,\n} from './types';\nimport { useFormikContext } from './FormikContext';\nimport { isFunction, isEmptyChildren, isObject } from './utils';\nimport invariant from 'tiny-warning';\n\nexport interface FieldProps<V = any, FormValues = any> {\n  field: FieldInputProps<V>;\n  form: FormikProps<FormValues>; // if ppl want to restrict this for a given form, let them.\n  meta: FieldMetaProps<V>;\n}\n\nexport interface FieldConfig<V = any> {\n  /**\n   * Field component to render. Can either be a string like 'select' or a component.\n   */\n  component?:\n  | string\n  | React.ComponentType<FieldProps<V>>\n  | React.ComponentType\n  | React.ForwardRefExoticComponent<any>;\n\n  /**\n   * Component to render. Can either be a string e.g. 'select', 'input', or 'textarea', or a component.\n   */\n  as?:\n  | React.ComponentType<FieldProps<V>['field']>\n  | string\n  | React.ComponentType\n  | React.ForwardRefExoticComponent<any>;\n\n  /**\n   * Render prop (works like React router's <Route render={props =>} />)\n   * @deprecated\n   */\n  render?: (props: FieldProps<V>) => React.ReactNode;\n\n  /**\n   * Children render function <Field name>{props => ...}</Field>)\n   */\n  children?: ((props: FieldProps<V>) => React.ReactNode) | React.ReactNode;\n\n  /**\n   * Validate a single field value independently\n   */\n  validate?: FieldValidator;\n\n  /**\n   * Used for 'select' and related input types.\n   */\n  multiple?: boolean;\n\n  /**\n   * Field name\n   */\n  name: string;\n\n  /** HTML input type */\n  type?: string;\n\n  /** Field value */\n  value?: any;\n\n  /** Inner ref */\n  innerRef?: (instance: any) => void;\n}\n\nexport type FieldAttributes<T> = { className?: string; } & GenericFieldHTMLAttributes &\n  FieldConfig<T> &\n  T & {\n    name: string,\n  };\n\nexport type FieldHookConfig<T> = GenericFieldHTMLAttributes & FieldConfig<T>;\n\nexport function useField<Val = any>(\n  propsOrFieldName: string | FieldHookConfig<Val>\n): [FieldInputProps<Val>, FieldMetaProps<Val>, FieldHelperProps<Val>] {\n  const formik = useFormikContext();\n  const {\n    getFieldProps,\n    getFieldMeta,\n    getFieldHelpers,\n    registerField,\n    unregisterField,\n  } = formik;\n\n  const isAnObject = isObject(propsOrFieldName);\n\n  // Normalize propsOrFieldName to FieldHookConfig<Val>\n  const props: FieldHookConfig<Val> = isAnObject\n    ? (propsOrFieldName as FieldHookConfig<Val>)\n    : { name: propsOrFieldName as string };\n\n  const { name: fieldName, validate: validateFn } = props;\n\n  React.useEffect(() => {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn,\n      });\n    }\n    return () => {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n\n  if (__DEV__) {\n    invariant(\n      formik,\n      'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component'\n    );\n  }\n\n  invariant(\n    fieldName,\n    'Invalid field name. Either pass `useField` a string or an object containing a `name` key.'\n  );\n\n  const fieldHelpers = React.useMemo(() => getFieldHelpers(fieldName), [\n    getFieldHelpers,\n    fieldName,\n  ]);\n\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\n\nexport function Field({\n  validate,\n  name,\n  render,\n  children,\n  as: is, // `as` is reserved in typescript lol\n  component,\n  className,\n  ...props\n}: FieldAttributes<any>) {\n  const {\n    validate: _validate,\n    validationSchema: _validationSchema,\n\n    ...formik\n  } = useFormikContext();\n\n  if (__DEV__) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      invariant(\n        !render,\n        `<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\"${name}\" render={({field, form}) => ...} /> with <Field name=\"${name}\">{({field, form, meta}) => ...}</Field>`\n      );\n\n      invariant(\n        !(is && children && isFunction(children)),\n        'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.'\n      );\n\n      invariant(\n        !(component && children && isFunction(children)),\n        'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.'\n      );\n\n      invariant(\n        !(render && children && !isEmptyChildren(children)),\n        'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored'\n      );\n      // eslint-disable-next-line\n    }, []);\n  }\n\n  // Register field and field-level validation with parent <Formik>\n  const { registerField, unregisterField } = formik;\n  React.useEffect(() => {\n    registerField(name, {\n      validate: validate,\n    });\n    return () => {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  const field = formik.getFieldProps({ name, ...props });\n  const meta = formik.getFieldMeta(name);\n  const legacyBag = { field, form: formik };\n\n  if (render) {\n    return render({ ...legacyBag, meta });\n  }\n\n  if (isFunction(children)) {\n    return children({ ...legacyBag, meta });\n  }\n\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      const { innerRef, ...rest } = props;\n      return React.createElement(\n        component,\n        { ref: innerRef, ...field, ...rest, className },\n        children\n      );\n    }\n    // We don't pass `meta` for backwards compat\n    return React.createElement(\n      component,\n      { field, form: formik, ...props, className },\n      children\n    );\n  }\n\n  // default to input here so we can check for both `as` and `children` above\n  const asElement = is || 'input';\n\n  if (typeof asElement === 'string') {\n    const { innerRef, ...rest } = props;\n    return React.createElement(\n      asElement,\n      { ref: innerRef, ...field, ...rest, className },\n      children\n    );\n  }\n\n  return React.createElement(asElement, { ...field, ...props, className }, children);\n}\n", "import * as React from 'react';\nimport { useFormikContext } from './FormikContext';\n\nexport type FormikFormProps = Pick<\n  React.FormHTMLAttributes<HTMLFormElement>,\n  Exclude<\n    keyof React.FormHTMLAttributes<HTMLFormElement>,\n    'onReset' | 'onSubmit'\n  >\n>;\n\ntype FormProps = React.ComponentPropsWithoutRef<'form'>;\n\n// @todo tests\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props: FormikFormProps, ref) => {\n    // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n    // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n    const { action, ...rest } = props;\n    const _action = action ?? '#';\n    const { handleReset, handleSubmit } = useFormikContext();\n    return (\n      <form\n        onSubmit={handleSubmit}\n        ref={ref}\n        onReset={handleReset}\n        action={_action}\n        {...rest}\n      />\n    );\n  }\n);\n\nForm.displayName = 'Form';\n", "import hoistNonReactStatics from 'hoist-non-react-statics';\nimport * as React from 'react';\nimport { Formik } from './Formik';\nimport {\n  FormikHelpers,\n  FormikProps,\n  FormikSharedConfig,\n  FormikValues,\n  FormikTouched,\n  FormikErrors,\n} from './types';\nimport { isFunction } from './utils';\n\n/**\n * State, handlers, and helpers injected as props into the wrapped form component.\n * Used with withFormik()\n *\n * @deprecated  Use `OuterProps & FormikProps<Values>` instead.\n */\nexport type InjectedFormikProps<Props, Values> = Props & FormikProps<Values>;\n\n/**\n * Formik helpers + { props }\n */\nexport type FormikBag<P, V> = { props: P } & FormikHelpers<V>;\n\n/**\n * withFormik() configuration options. Backwards compatible.\n */\nexport interface WithFormikConfig<\n  Props,\n  Values extends FormikValues = FormikValues,\n  DeprecatedPayload = Values\n> extends FormikSharedConfig<Props> {\n  /**\n   * Set the display name of the component. Useful for React DevTools.\n   */\n  displayName?: string;\n\n  /**\n   * Submission handler\n   */\n  handleSubmit: (values: Values, formikBag: FormikBag<Props, Values>) => void;\n\n  /**\n   * Map props to the form values\n   */\n  mapPropsToValues?: (props: Props) => Values;\n\n  /**\n   * Map props to the form status\n   */\n  mapPropsToStatus?: (props: Props) => any;\n\n  /**\n   * Map props to the form touched state\n   */\n  mapPropsToTouched?: (props: Props) => FormikTouched<Values>;\n\n  /**\n   * Map props to the form errors state\n   */\n  mapPropsToErrors?: (props: Props) => FormikErrors<Values>;\n\n  /**\n   * @deprecated in 0.9.0 (but needed to break TS types)\n   */\n  mapValuesToPayload?: (values: Values) => DeprecatedPayload;\n\n  /**\n   * A Yup Schema or a function that returns a Yup schema\n   */\n  validationSchema?: any | ((props: Props) => any);\n\n  /**\n   * Validation function. Must return an error object or promise that\n   * throws an error object where that object keys map to corresponding value.\n   */\n  validate?: (values: Values, props: Props) => void | object | Promise<any>;\n}\n\nexport type CompositeComponent<P> =\n  | React.ComponentClass<P>\n  | React.FunctionComponent<P>;\n\nexport interface ComponentDecorator<TOwnProps, TMergedProps> {\n  (component: CompositeComponent<TMergedProps>): React.ComponentType<TOwnProps>;\n}\n\nexport interface InferableComponentDecorator<TOwnProps> {\n  <T extends CompositeComponent<TOwnProps>>(component: T): T;\n}\n\n/**\n * A public higher-order component to access the imperative API\n */\nexport function withFormik<\n  OuterProps extends object,\n  Values extends FormikValues,\n  Payload = Values\n>({\n  mapPropsToValues = (vanillaProps: OuterProps): Values => {\n    let val: Values = {} as Values;\n    for (let k in vanillaProps) {\n      if (\n        vanillaProps.hasOwnProperty(k) &&\n        typeof vanillaProps[k] !== 'function'\n      ) {\n        // @todo TypeScript fix\n        (val as any)[k] = vanillaProps[k];\n      }\n    }\n    return val as Values;\n  },\n  ...config\n}: WithFormikConfig<OuterProps, Values, Payload>): ComponentDecorator<\n  OuterProps,\n  OuterProps & FormikProps<Values>\n> {\n  return function createFormik(\n    Component: CompositeComponent<OuterProps & FormikProps<Values>>\n  ): React.ComponentClass<OuterProps> {\n    const componentDisplayName =\n      Component.displayName ||\n      Component.name ||\n      (Component.constructor && Component.constructor.name) ||\n      'Component';\n    /**\n     * We need to use closures here for to provide the wrapped component's props to\n     * the respective withFormik config methods.\n     */\n    class C extends React.Component<OuterProps, {}> {\n      static displayName = `WithFormik(${componentDisplayName})`;\n\n      validate = (values: Values): void | object | Promise<any> => {\n        return config.validate!(values, this.props);\n      };\n\n      validationSchema = () => {\n        return isFunction(config.validationSchema)\n          ? config.validationSchema!(this.props)\n          : config.validationSchema;\n      };\n\n      handleSubmit = (values: Values, actions: FormikHelpers<Values>) => {\n        return config.handleSubmit(values, {\n          ...actions,\n          props: this.props,\n        });\n      };\n\n      /**\n       * Just avoiding a render callback for perf here\n       */\n      renderFormComponent = (formikProps: FormikProps<Values>) => {\n        return <Component {...this.props} {...formikProps} />;\n      };\n\n      render() {\n        const { children, ...props } = this.props as any;\n        return (\n          <Formik\n            {...props}\n            {...config}\n            validate={config.validate && this.validate}\n            validationSchema={config.validationSchema && this.validationSchema}\n            initialValues={mapPropsToValues(this.props)}\n            initialStatus={\n              config.mapPropsToStatus && config.mapPropsToStatus(this.props)\n            }\n            initialErrors={\n              config.mapPropsToErrors && config.mapPropsToErrors(this.props)\n            }\n            initialTouched={\n              config.mapPropsToTouched && config.mapPropsToTouched(this.props)\n            }\n            onSubmit={this.handleSubmit as any}\n            children={this.renderFormComponent}\n          />\n        );\n      }\n    }\n\n    return hoistNonReactStatics(\n      C,\n      Component as React.ComponentClass<OuterProps & FormikProps<Values>> // cast type to ComponentClass (even if SFC)\n    ) as React.ComponentClass<OuterProps>;\n  };\n}\n", "import * as React from 'react';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\nimport { FormikContextType } from './types';\nimport { FormikConsumer } from './FormikContext';\nimport invariant from 'tiny-warning';\n\n/**\n * Connect any component to Formik context, and inject as a prop called `formik`;\n * @param Comp React Component\n */\nexport function connect<OuterProps, Values = {}>(\n  Comp: React.ComponentType<OuterProps & { formik: FormikContextType<Values> }>\n) {\n  const C: React.FC<OuterProps> = props => (\n    <FormikConsumer>\n      {formik => {\n        invariant(\n          !!formik,\n          `Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: ${Comp.name}`\n        );\n        return <Comp {...props} formik={formik} />;\n      }}\n    </FormikConsumer>\n  );\n\n  const componentDisplayName =\n    Comp.displayName ||\n    Comp.name ||\n    (Comp.constructor && Comp.constructor.name) ||\n    'Component';\n\n  // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n  (C as React.FC<OuterProps> & {\n    WrappedComponent: typeof Comp;\n  }).WrappedComponent = Comp;\n\n  C.displayName = `FormikConnect(${componentDisplayName})`;\n\n  return hoistNonReactStatics(\n    C,\n    Comp as React.ComponentClass<\n      OuterProps & { formik: FormikContextType<Values> }\n    > // cast type to ComponentClass (even if SFC)\n  );\n}\n", "import cloneDeep from 'lodash/cloneDeep';\nimport * as React from 'react';\nimport isEqual from 'react-fast-compare';\nimport { connect } from './connect';\nimport {\n  FormikContextType,\n  FormikProps,\n  FormikState,\n  SharedRenderProps,\n} from './types';\nimport {\n  getIn,\n  isEmptyArray,\n  isEmptyChildren,\n  isFunction,\n  isObject,\n  setIn,\n} from './utils';\n\nexport type FieldArrayRenderProps = ArrayHelpers & {\n  form: FormikProps<any>;\n  name: string;\n};\n\nexport type FieldArrayConfig = {\n  /** Really the path to the array field to be updated */\n  name: string;\n  /** Should field array validate the form AFTER array updates/changes? */\n  validateOnChange?: boolean;\n} & SharedRenderProps<FieldArrayRenderProps>;\nexport interface ArrayHelpers<T extends any[] = any[]> {\n  /** Imperatively add a value to the end of an array */\n  push<X = T[number]>(obj: X): void;\n  /** Curried fn to add a value to the end of an array */\n  handlePush<X = T[number]>(obj: X): () => void;\n  /** Imperatively swap two values in an array */\n  swap: (indexA: number, indexB: number) => void;\n  /** Curried fn to swap two values in an array */\n  handleSwap: (indexA: number, indexB: number) => () => void;\n  /** Imperatively move an element in an array to another index */\n  move: (from: number, to: number) => void;\n  /** Imperatively move an element in an array to another index */\n  handleMove: (from: number, to: number) => () => void;\n  /** Imperatively insert an element at a given index into the array */\n  insert<X = T[number]>(index: number, value: X): void;\n  /** Curried fn to insert an element at a given index into the array */\n  handleInsert<X = T[number]>(index: number, value: X): () => void;\n  /** Imperatively replace a value at an index of an array  */\n  replace<X = T[number]>(index: number, value: X): void;\n  /** Curried fn to replace an element at a given index into the array */\n  handleReplace<X = T[number]>(index: number, value: X): () => void;\n  /** Imperatively add an element to the beginning of an array and return its length */\n  unshift<X = T[number]>(value: X): number;\n  /** Curried fn to add an element to the beginning of an array */\n  handleUnshift<X = T[number]>(value: X): () => void;\n  /** Curried fn to remove an element at an index of an array */\n  handleRemove: (index: number) => () => void;\n  /** Curried fn to remove a value from the end of the array */\n  handlePop: () => () => void;\n  /** Imperatively remove and element at an index of an array */\n  remove<X = T[number]>(index: number): X | undefined;\n  /** Imperatively remove and return value from the end of the array */\n  pop<X = T[number]>(): X | undefined;\n}\n\n/**\n * Some array helpers!\n */\nexport const move = <T,>(array: T[], from: number, to: number) => {\n  const copy = copyArrayLike(array);\n  const value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\n\nexport const swap = <T,>(\n  arrayLike: ArrayLike<T>,\n  indexA: number,\n  indexB: number\n) => {\n  const copy = copyArrayLike(arrayLike);\n  const a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\n\nexport const insert = <T,>(\n  arrayLike: ArrayLike<T>,\n  index: number,\n  value: T\n) => {\n  const copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\n\nexport const replace = <T,>(\n  arrayLike: ArrayLike<T>,\n  index: number,\n  value: T\n) => {\n  const copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\n\nconst copyArrayLike = (arrayLike: ArrayLike<any>) => {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [...arrayLike];\n  } else {\n    const maxIndex = Object.keys(arrayLike)\n      .map(key => parseInt(key))\n      .reduce((max, el) => (el > max ? el : max), 0);\n    return Array.from({ ...arrayLike, length: maxIndex + 1 });\n  }\n};\n\nconst createAlterationHandler = (\n  alteration: boolean | Function,\n  defaultFunction: Function\n) => {\n  const fn = typeof alteration === 'function' ? alteration : defaultFunction;\n\n  return (data: any | any[]) => {\n    if (Array.isArray(data) || isObject(data)) {\n      const clone = copyArrayLike(data);\n      return fn(clone);\n    }\n\n    // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n    return data;\n  };\n};\n\nclass FieldArrayInner<Values = {}> extends React.Component<\n  FieldArrayConfig & { formik: FormikContextType<Values> },\n  {}\n> {\n  static defaultProps = {\n    validateOnChange: true,\n  };\n\n  constructor(props: FieldArrayConfig & { formik: FormikContextType<Values> }) {\n    super(props);\n    // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n    this.remove = this.remove.bind(this) as any;\n    this.pop = this.pop.bind(this) as any;\n  }\n\n  componentDidUpdate(\n    prevProps: FieldArrayConfig & { formik: FormikContextType<Values> }\n  ) {\n    if (\n      this.props.validateOnChange &&\n      this.props.formik.validateOnChange &&\n      !isEqual(\n        getIn(prevProps.formik.values, prevProps.name),\n        getIn(this.props.formik.values, this.props.name)\n      )\n    ) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  }\n\n  updateArrayField = (\n    fn: Function,\n    alterTouched: boolean | Function,\n    alterErrors: boolean | Function\n  ) => {\n    const {\n      name,\n\n      formik: { setFormikState },\n    } = this.props;\n\n    setFormikState((prevState: FormikState<any>) => {\n      let updateErrors = createAlterationHandler(alterErrors, fn);\n      let updateTouched = createAlterationHandler(alterTouched, fn);\n\n      // values fn should be executed before updateErrors and updateTouched,\n      // otherwise it causes an error with unshift.\n      let values = setIn(\n        prevState.values,\n        name,\n        fn(getIn(prevState.values, name))\n      );\n\n      let fieldError = alterErrors\n        ? updateErrors(getIn(prevState.errors, name))\n        : undefined;\n      let fieldTouched = alterTouched\n        ? updateTouched(getIn(prevState.touched, name))\n        : undefined;\n\n      if (isEmptyArray(fieldError)) {\n        fieldError = undefined;\n      }\n      if (isEmptyArray(fieldTouched)) {\n        fieldTouched = undefined;\n      }\n\n      return {\n        ...prevState,\n        values,\n        errors: alterErrors\n          ? setIn(prevState.errors, name, fieldError)\n          : prevState.errors,\n        touched: alterTouched\n          ? setIn(prevState.touched, name, fieldTouched)\n          : prevState.touched,\n      };\n    });\n  };\n\n  push = (value: any) =>\n    this.updateArrayField(\n      (arrayLike: ArrayLike<any>) => [\n        ...copyArrayLike(arrayLike),\n        cloneDeep(value),\n      ],\n      false,\n      false\n    );\n\n  handlePush = (value: any) => () => this.push(value);\n\n  swap = (indexA: number, indexB: number) =>\n    this.updateArrayField(\n      (array: any[]) => swap(array, indexA, indexB),\n      true,\n      true\n    );\n\n  handleSwap = (indexA: number, indexB: number) => () =>\n    this.swap(indexA, indexB);\n\n  move = (from: number, to: number) =>\n    this.updateArrayField((array: any[]) => move(array, from, to), true, true);\n\n  handleMove = (from: number, to: number) => () => this.move(from, to);\n\n  insert = (index: number, value: any) =>\n    this.updateArrayField(\n      (array: any[]) => insert(array, index, value),\n      (array: any[]) => insert(array, index, null),\n      (array: any[]) => insert(array, index, null)\n    );\n\n  handleInsert = (index: number, value: any) => () => this.insert(index, value);\n\n  replace = (index: number, value: any) =>\n    this.updateArrayField(\n      (array: any[]) => replace(array, index, value),\n      false,\n      false\n    );\n\n  handleReplace = (index: number, value: any) => () =>\n    this.replace(index, value);\n\n  unshift = (value: any) => {\n    let length = -1;\n    this.updateArrayField(\n      (array: any[]) => {\n        const arr = array ? [value, ...array] : [value];\n\n        length = arr.length;\n\n        return arr;\n      },\n      (array: any[]) => {\n        return array ? [null, ...array] : [null];\n      },\n      (array: any[]) => {\n        return array ? [null, ...array] : [null];\n      }\n    );\n\n    return length;\n  };\n\n  handleUnshift = (value: any) => () => this.unshift(value);\n\n  remove<T>(index: number): T {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    let result: any;\n    this.updateArrayField(\n      // so this gets call 3 times\n      (array?: any[]) => {\n        const copy = array ? copyArrayLike(array) : [];\n        if (!result) {\n          result = copy[index];\n        }\n        if (isFunction(copy.splice)) {\n          copy.splice(index, 1);\n        }\n        // if the array only includes undefined values we have to return an empty array\n        return isFunction(copy.every)\n          ? copy.every(v => v === undefined)\n            ? []\n            : copy\n          : copy;\n      },\n      true,\n      true\n    );\n\n    return result as T;\n  }\n\n  handleRemove = (index: number) => () => this.remove<any>(index);\n\n  pop<T>(): T {\n    // Remove relevant pieces of `touched` and `errors` too!\n    let result: any;\n    this.updateArrayField(\n      // so this gets call 3 times\n      (array: any[]) => {\n        const tmp = array.slice();\n        if (!result) {\n          result = tmp && tmp.pop && tmp.pop();\n        }\n        return tmp;\n      },\n      true,\n      true\n    );\n\n    return result as T;\n  }\n\n  handlePop = () => () => this.pop<any>();\n\n  render() {\n    const arrayHelpers: ArrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove,\n    };\n\n    const {\n      component,\n      render,\n      children,\n      name,\n      formik: {\n        validate: _validate,\n        validationSchema: _validationSchema,\n        ...restOfFormik\n      },\n    } = this.props;\n\n    const props: FieldArrayRenderProps = {\n      ...arrayHelpers,\n      form: restOfFormik,\n      name,\n    };\n\n    return component\n      ? React.createElement(component as any, props)\n      : render\n      ? (render as any)(props)\n      : children // children come last, always called\n      ? typeof children === 'function'\n        ? (children as any)(props)\n        : !isEmptyChildren(children)\n        ? React.Children.only(children)\n        : null\n      : null;\n  }\n}\n\nexport const FieldArray = connect<FieldArrayConfig, any>(FieldArrayInner);\n", "import * as React from 'react';\nimport { FormikContextType } from './types';\nimport { getIn, isFunction } from './utils';\nimport { connect } from './connect';\n\nexport interface ErrorMessageProps {\n  id?: string;\n  name: string;\n  className?: string;\n  component?: string | React.ComponentType;\n  children?: (errorMessage: string) => React.ReactNode;\n  render?: (errorMessage: string) => React.ReactNode;\n}\n\nclass ErrorMessageImpl extends React.Component<\n  ErrorMessageProps & { formik: FormikContextType<any> }\n> {\n  shouldComponentUpdate(\n    props: ErrorMessageProps & { formik: FormikContextType<any> }\n  ) {\n    if (\n      getIn(this.props.formik.errors, this.props.name) !==\n        getIn(props.formik.errors, this.props.name) ||\n      getIn(this.props.formik.touched, this.props.name) !==\n        getIn(props.formik.touched, this.props.name) ||\n      Object.keys(this.props).length !== Object.keys(props).length\n    ) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  render() {\n    let { component, formik, render, children, name, ...rest } = this.props;\n\n    const touch = getIn(formik.touched, name);\n    const error = getIn(formik.errors, name);\n\n    return !!touch && !!error\n      ? render\n        ? isFunction(render)\n          ? render(error)\n          : null\n        : children\n        ? isFunction(children)\n          ? children(error)\n          : null\n        : component\n        ? React.createElement(component, rest as any, error)\n        : error\n      : null;\n  }\n}\n\nexport const ErrorMessage = connect<\n  ErrorMessageProps,\n  ErrorMessageProps & { formik: FormikContextType<any> }\n>(ErrorMessageImpl);\n", "import * as React from 'react';\n\nimport {\n  FormikProps,\n  GenericFieldHTMLAttributes,\n  FormikContextType,\n  FieldMetaProps,\n  FieldInputProps,\n} from './types';\nimport invariant from 'tiny-warning';\nimport { getIn, isEmptyChildren, isFunction } from './utils';\nimport { FieldConfig } from './Field';\nimport { connect } from './connect';\n\ntype $FixMe = any;\n\nexport interface FastFieldProps<V = any> {\n  field: FieldInputProps<V>;\n  meta: FieldMetaProps<V>;\n  form: FormikProps<V>; // if ppl want to restrict this for a given form, let them.\n}\n\nexport type FastFieldConfig<T> = FieldConfig & {\n  /** Override FastField's default shouldComponentUpdate */\n  shouldUpdate?: (\n    nextProps: T & GenericFieldHTMLAttributes,\n    props: {}\n  ) => boolean;\n};\n\nexport type FastFieldAttributes<T> = GenericFieldHTMLAttributes &\n  FastFieldConfig<T> &\n  T;\n\ntype FastFieldInnerProps<Values = {}, Props = {}> = FastFieldAttributes<\n  Props\n> & { formik: FormikContextType<Values> };\n\n/**\n * Custom Field component for quickly hooking into Formik\n * context and wiring up forms.\n */\nclass FastFieldInner<Values = {}, Props = {}> extends React.Component<\n  FastFieldInnerProps<Values, Props>,\n  {}\n> {\n  constructor(props: FastFieldInnerProps<Values, Props>) {\n    super(props);\n    const { render, children, component, as: is, name } = props;\n    invariant(\n      !render,\n      `<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={${name}}>{props => ...}</FastField> instead.`\n    );\n    invariant(\n      !(component && render),\n      'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored'\n    );\n\n    invariant(\n      !(is && children && isFunction(children)),\n      'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.'\n    );\n\n    invariant(\n      !(component && children && isFunction(children)),\n      'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.'\n    );\n\n    invariant(\n      !(render && children && !isEmptyChildren(children)),\n      'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored'\n    );\n  }\n\n  shouldComponentUpdate(props: FastFieldInnerProps<Values, Props>) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (\n      props.name !== this.props.name ||\n      getIn(props.formik.values, this.props.name) !==\n        getIn(this.props.formik.values, this.props.name) ||\n      getIn(props.formik.errors, this.props.name) !==\n        getIn(this.props.formik.errors, this.props.name) ||\n      getIn(props.formik.touched, this.props.name) !==\n        getIn(this.props.formik.touched, this.props.name) ||\n      Object.keys(this.props).length !== Object.keys(props).length ||\n      props.formik.isSubmitting !== this.props.formik.isSubmitting\n    ) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate,\n    });\n  }\n\n  componentDidUpdate(prevProps: FastFieldAttributes<Props>) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate,\n      });\n    }\n\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate,\n      });\n    }\n  }\n\n  componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  }\n\n  render() {\n    const {\n      validate,\n      name,\n      render,\n      as: is,\n      children,\n      component,\n      shouldUpdate,\n      formik,\n      ...props\n    } = this.props as FastFieldInnerProps<Values, Props>;\n\n    const {\n      validate: _validate,\n      validationSchema: _validationSchema,\n      ...restOfFormik\n    } = formik;\n    const field = formik.getFieldProps({ name, ...props });\n    const meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name),\n    };\n\n    const bag = { field, meta, form: restOfFormik };\n\n    if (render) {\n      return (render as any)(bag);\n    }\n\n    if (isFunction(children)) {\n      return (children as (props: FastFieldProps<any>) => React.ReactNode)(bag);\n    }\n\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        const { innerRef, ...rest } = props;\n        return React.createElement(\n          component,\n          { ref: innerRef, ...field, ...(rest as $FixMe) },\n          children\n        );\n      }\n      // We don't pass `meta` for backwards compat\n      return React.createElement(\n        component as React.ComponentClass<$FixMe>,\n        { field, form: formik, ...props },\n        children\n      );\n    }\n\n    // default to input here so we can check for both `as` and `children` above\n    const asElement = is || 'input';\n\n    if (typeof asElement === 'string') {\n      const { innerRef, ...rest } = props;\n      return React.createElement(\n        asElement,\n        { ref: innerRef, ...field, ...(rest as $FixMe) },\n        children\n      );\n    }\n\n    return React.createElement(\n      asElement as React.ComponentClass,\n      { ...field, ...props },\n      children\n    );\n  }\n}\n\nexport const FastField = connect<FastFieldAttributes<any>, any>(FastFieldInner);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAIaA,aAAa,gBAAGC,aAAA,CAC3BC,SAD2B;AAG7BF,aAAa,CAACG,WAAd,GAA4B,eAA5B;IAEaC,cAAc,GAAGJ,aAAa,CAACK,QAAA;IAC/BC,cAAc,GAAGN,aAAa,CAACO,QAAA;SAE5BC,iBAAA;EACd,IAAMC,MAAM,GAAGC,UAAA,CAA4CV,aAA5C,CAAf;EAEA,CACE,CAAC,CAACS,MADJ,GAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,0HAAT,GAAAA,SAAS,OAAT;EAKA,OAAOL,MAAP;AACD;;ACfD;;AACA,IAAaM,YAAY,GAAG,SAAfA,YAAeA,CAACC,KAAD;EAAA,OAC1BC,KAAK,CAACC,OAAN,CAAcF,KAAd,KAAwBA,KAAK,CAACG,MAAN,KAAiB,CADf;AAAA,CAArB;AAGP;;AACA,IAAaC,UAAU,GAAG,SAAbA,UAAaA,CAACC,GAAD;EAAA,OACxB,OAAOA,GAAP,KAAe,UADS;AAAA,CAAnB;AAGP;;AACA,IAAaC,QAAQ,GAAG,SAAXA,QAAWA,CAACD,GAAD;EAAA,OACtBA,GAAG,KAAK,IAAR,IAAgB,OAAOA,GAAP,KAAe,QADT;AAAA,CAAjB;AAGP;;AACA,IAAaE,SAAS,GAAG,SAAZA,SAAYA,CAACF,GAAD;EAAA,OACvBG,MAAM,CAACC,IAAI,CAACC,KAAL,CAAWC,MAAM,CAACN,GAAD,CAAjB,CAAD,CAAN,KAAoCA,GADb;AAAA,CAAlB;AAGP;;AACA,IAAaO,QAAQ,GAAG,SAAXA,QAAWA,CAACP,GAAD;EAAA,OACtBQ,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BX,GAA/B,MAAwC,iBADlB;AAAA,CAAjB;AAGP;AACA;;AACA,IAAaY,OAAK,GAAG,SAARC,KAAQA,CAACb,GAAD;EAAA,OAAuBA,GAAG,KAAKA,GAA/B;AAAA,CAAd;AAEP;;AACA,IAAac,eAAe,GAAG,SAAlBA,eAAkBA,CAACC,QAAD;EAAA,OAC7BC,QAAA,CAAeC,KAAf,CAAqBF,QAArB,MAAmC,CADN;AAAA,CAAxB;AAGP;;AACA,IAAaG,SAAS,GAAG,SAAZA,SAAYA,CAACvB,KAAD;EAAA,OACvBM,QAAQ,CAACN,KAAD,CAAR,IAAmBI,UAAU,CAACJ,KAAK,CAACwB,IAAP,CADN;AAAA,CAAlB;AAGP;;AACA,IAAaC,YAAY,GAAG,SAAfA,YAAeA,CAACzB,KAAD;EAAA,OAC1BA,KAAK,IAAIM,QAAQ,CAACN,KAAD,CAAjB,IAA4BM,QAAQ,CAACN,KAAK,CAAC0B,MAAP,CADV;AAAA,CAArB;AAGP;;;;;;;;;;;;AAWA,SAAgBC,iBAAiBC,GAAA;EAC/BA,GAAG,GAAGA,GAAG,KAAK,OAAOC,QAAP,KAAoB,WAApB,GAAkCA,QAAlC,GAA6C3C,SAAlD,CAAT;EACA,IAAI,OAAO0C,GAAP,KAAe,WAAnB,EAAgC;IAC9B,OAAO,IAAP;EACD;EACD,IAAI;IACF,OAAOA,GAAG,CAACE,aAAJ,IAAqBF,GAAG,CAACG,IAAhC;EACD,CAFD,CAEE,OAAOC,CAAP,EAAU;IACV,OAAOJ,GAAG,CAACG,IAAX;EACD;AACF;AAED;;;;AAGA,SAAgBE,MACd5B,GAAA,EACA6B,GAAA,EACAC,GAAA,EACAC,CAAA;MAAAA,CAAA;IAAAA,CAAA,GAAY;;EAEZ,IAAMC,IAAI,GAAGC,MAAM,CAACJ,GAAD,CAAnB;EACA,OAAO7B,GAAG,IAAI+B,CAAC,GAAGC,IAAI,CAAClC,MAAvB,EAA+B;IAC7BE,GAAG,GAAGA,GAAG,CAACgC,IAAI,CAACD,CAAC,EAAF,CAAL,CAAT;EACD;;EAGD,IAAIA,CAAC,KAAKC,IAAI,CAAClC,MAAX,IAAqB,CAACE,GAA1B,EAA+B;IAC7B,OAAO8B,GAAP;EACD;EAED,OAAO9B,GAAG,KAAKnB,SAAR,GAAoBiD,GAApB,GAA0B9B,GAAjC;AACD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAgBkC,MAAMlC,GAAA,EAAUgC,IAAA,EAAcrC,KAAA;EAC5C,IAAIwC,GAAG,GAAQC,KAAK,CAACpC,GAAD,CAApB;;EACA,IAAIqC,MAAM,GAAQF,GAAlB;EACA,IAAIG,CAAC,GAAG,CAAR;EACA,IAAIC,SAAS,GAAGN,MAAM,CAACD,IAAD,CAAtB;EAEA,OAAOM,CAAC,GAAGC,SAAS,CAACzC,MAAV,GAAmB,CAA9B,EAAiCwC,CAAC,EAAlC,EAAsC;IACpC,IAAME,WAAW,GAAWD,SAAS,CAACD,CAAD,CAArC;IACA,IAAIG,UAAU,GAAQb,KAAK,CAAC5B,GAAD,EAAMuC,SAAS,CAACG,KAAV,CAAgB,CAAhB,EAAmBJ,CAAC,GAAG,CAAvB,CAAN,CAA3B;IAEA,IAAIG,UAAU,KAAKxC,QAAQ,CAACwC,UAAD,CAAR,IAAwB7C,KAAK,CAACC,OAAN,CAAc4C,UAAd,CAA7B,CAAd,EAAuE;MACrEJ,MAAM,GAAGA,MAAM,CAACG,WAAD,CAAN,GAAsBJ,KAAK,CAACK,UAAD,CAApC;IACD,CAFD,MAEO;MACL,IAAME,QAAQ,GAAWJ,SAAS,CAACD,CAAC,GAAG,CAAL,CAAlC;MACAD,MAAM,GAAGA,MAAM,CAACG,WAAD,CAAN,GACPtC,SAAS,CAACyC,QAAD,CAAT,IAAuBrC,MAAM,CAACqC,QAAD,CAAN,IAAoB,CAA3C,GAA+C,EAA/C,GAAoD,EADtD;IAED;EACF;;EAGD,IAAI,CAACL,CAAC,KAAK,CAAN,GAAUtC,GAAV,GAAgBqC,MAAjB,EAAyBE,SAAS,CAACD,CAAD,CAAlC,MAA2C3C,KAA/C,EAAsD;IACpD,OAAOK,GAAP;EACD;EAED,IAAIL,KAAK,KAAKd,SAAd,EAAyB;IACvB,OAAOwD,MAAM,CAACE,SAAS,CAACD,CAAD,CAAV,CAAb;EACD,CAFD,MAEO;IACLD,MAAM,CAACE,SAAS,CAACD,CAAD,CAAV,CAAN,GAAuB3C,KAAvB;EACD;EAGD;;EACA,IAAI2C,CAAC,KAAK,CAAN,IAAW3C,KAAK,KAAKd,SAAzB,EAAoC;IAClC,OAAOsD,GAAG,CAACI,SAAS,CAACD,CAAD,CAAV,CAAV;EACD;EAED,OAAOH,GAAP;AACD;AAED;;;;;;;;AAOA,SAAgBS,sBACdC,MAAA,EACAlD,KAAA,EACAmD,OAAA,EACAC,QAAA;MADAD,OAAA;IAAAA,OAAA,GAAe,IAAIE,OAAJ;;MACfD,QAAA;IAAAA,QAAA,GAAgB;;EAEhB,SAAAE,EAAA,MAAAC,YAAA,GAAc1C,MAAM,CAAC2C,IAAP,CAAYN,MAAZ,CAAd,EAAAI,EAAA,GAAAC,YAAA,CAAApD,MAAA,EAAAmD,EAAA,IAAmC;IAA9B,IAAIG,CAAC,GAAAF,YAAA,CAAAD,EAAA,CAAL;IACH,IAAMI,GAAG,GAAGR,MAAM,CAACO,CAAD,CAAlB;IACA,IAAInD,QAAQ,CAACoD,GAAD,CAAZ,EAAmB;MACjB,IAAI,CAACP,OAAO,CAACQ,GAAR,CAAYD,GAAZ,CAAL,EAAuB;QACrBP,OAAO,CAACS,GAAR,CAAYF,GAAZ,EAAiB,IAAjB,EADqB;QAGrB;QACA;;QACAN,QAAQ,CAACK,CAAD,CAAR,GAAcxD,KAAK,CAACC,OAAN,CAAcwD,GAAd,IAAqB,EAArB,GAA0B,EAAxC;QACAT,qBAAqB,CAACS,GAAD,EAAM1D,KAAN,EAAamD,OAAb,EAAsBC,QAAQ,CAACK,CAAD,CAA9B,CAArB;MACD;IACF,CATD,MASO;MACLL,QAAQ,CAACK,CAAD,CAAR,GAAczD,KAAd;IACD;EACF;EAED,OAAOoD,QAAP;AACD;AC5HD,SAASS,aAATA,CACEC,KADF,EAEEC,GAFF;EAIE,QAAQA,GAAG,CAACC,IAAZ;IACE,KAAK,YAAL;MACE,OAAAC,QAAA,KAAYH,KAAZ;QAAmBI,MAAM,EAAEH,GAAG,CAACI;MAA/B;IACF,KAAK,aAAL;MACE,OAAAF,QAAA,KAAYH,KAAZ;QAAmBM,OAAO,EAAEL,GAAG,CAACI;MAAhC;IACF,KAAK,YAAL;MACE,IAAIE,OAAO,CAACP,KAAK,CAACQ,MAAP,EAAeP,GAAG,CAACI,OAAnB,CAAX,EAAwC;QACtC,OAAOL,KAAP;MACD;MAED,OAAAG,QAAA,KAAYH,KAAZ;QAAmBQ,MAAM,EAAEP,GAAG,CAACI;MAA/B;IACF,KAAK,YAAL;MACE,OAAAF,QAAA,KAAYH,KAAZ;QAAmBS,MAAM,EAAER,GAAG,CAACI;MAA/B;IACF,KAAK,kBAAL;MACE,OAAAF,QAAA,KAAYH,KAAZ;QAAmBU,YAAY,EAAET,GAAG,CAACI;MAArC;IACF,KAAK,kBAAL;MACE,OAAAF,QAAA,KAAYH,KAAZ;QAAmBW,YAAY,EAAEV,GAAG,CAACI;MAArC;IACF,KAAK,iBAAL;MACE,OAAAF,QAAA,KACKH,KADL;QAEEI,MAAM,EAAE3B,KAAK,CAACuB,KAAK,CAACI,MAAP,EAAeH,GAAG,CAACI,OAAJ,CAAYO,KAA3B,EAAkCX,GAAG,CAACI,OAAJ,CAAYnE,KAA9C;MAFf;IAIF,KAAK,mBAAL;MACE,OAAAiE,QAAA,KACKH,KADL;QAEEM,OAAO,EAAE7B,KAAK,CAACuB,KAAK,CAACM,OAAP,EAAgBL,GAAG,CAACI,OAAJ,CAAYO,KAA5B,EAAmCX,GAAG,CAACI,OAAJ,CAAYnE,KAA/C;MAFhB;IAIF,KAAK,iBAAL;MACE,OAAAiE,QAAA,KACKH,KADL;QAEEQ,MAAM,EAAE/B,KAAK,CAACuB,KAAK,CAACQ,MAAP,EAAeP,GAAG,CAACI,OAAJ,CAAYO,KAA3B,EAAkCX,GAAG,CAACI,OAAJ,CAAYnE,KAA9C;MAFf;IAIF,KAAK,YAAL;MACE,OAAAiE,QAAA,KAAYH,KAAZ,EAAsBC,GAAG,CAACI,OAA1B;IACF,KAAK,kBAAL;MACE,OAAOJ,GAAG,CAACI,OAAJ,CAAYL,KAAZ,CAAP;IACF,KAAK,gBAAL;MACE,OAAAG,QAAA,KACKH,KADL;QAEEM,OAAO,EAAEnB,qBAAqB,CAC5Ba,KAAK,CAACI,MADsB,EAE5B,IAF4B,CAFhC;QAMEM,YAAY,EAAE,IANhB;QAOEG,WAAW,EAAEb,KAAK,CAACa,WAAN,GAAoB;MAPnC;IASF,KAAK,gBAAL;MACE,OAAAV,QAAA,KACKH,KADL;QAEEU,YAAY,EAAE;MAFhB;IAIF,KAAK,gBAAL;MACE,OAAAP,QAAA,KACKH,KADL;QAEEU,YAAY,EAAE;MAFhB;IAIF;MACE,OAAOV,KAAP;EAzDJ;AA2DD;;AAGD,IAAMc,WAAW,GAA0B,EAA3C;AACA,IAAMC,YAAY,GAA2B,EAA7C;AAUA,SAAgBC,UAAAC,IAAA;mCACdC,gBAAA;IAAAA,gBAAA,GAAAC,qBAAA,cAAmB,OAAAA,qBAAA;+BACnBC,cAAA;IAAAA,cAAA,GAAAC,mBAAA,cAAiB,OAAAA,mBAAA;gCACjBC,eAAA;IAAAA,eAAA,GAAAC,oBAAA,cAAkB,QAAAA,oBAAA;IAClBC,cAAA,GAAAP,IAAA,CAAAO,cAAA;iCACAC,kBAAA;IAAAA,kBAAA,GAAAC,qBAAA,cAAqB,QAAAA,qBAAA;IACrBC,QAAA,GAAAV,IAAA,CAAAU,QAAA;IACGC,IAAA,GAAAC,6BAAA,CAAAZ,IAAA;EAEH,IAAMa,KAAK,GAAA3B,QAAA;IACTe,gBAAgB,EAAhBA,gBADS;IAETE,cAAc,EAAdA,cAFS;IAGTE,eAAe,EAAfA,eAHS;IAITK,QAAQ,EAARA;EAJS,GAKNC,IALM,CAAX;EAOA,IAAMG,aAAa,GAAGC,MAAA,CAAaF,KAAK,CAACC,aAAnB,CAAtB;EACA,IAAME,aAAa,GAAGD,MAAA,CAAaF,KAAK,CAACG,aAAN,IAAuBnB,WAApC,CAAtB;EACA,IAAMoB,cAAc,GAAGF,MAAA,CAAaF,KAAK,CAACI,cAAN,IAAwBnB,YAArC,CAAvB;EACA,IAAMoB,aAAa,GAAGH,MAAA,CAAaF,KAAK,CAACK,aAAnB,CAAtB;EACA,IAAMC,SAAS,GAAGJ,MAAA,CAAsB,KAAtB,CAAlB;EACA,IAAMK,aAAa,GAAGL,MAAA,CAA4B,EAA5B,CAAtB;EACA,IAAAnG,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;IACX;IACAuG,SAAA,CAAgB;MACd,EACE,OAAOd,cAAP,KAA0B,WAD5B,IAAA3F,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,2IAFO,CAAT,GAAAA,SAAS,OAAT;IAKD,CAND,EAMG,EANH;EAOD;EAEDsG,SAAA,CAAgB;IACdF,SAAS,CAACG,OAAV,GAAoB,IAApB;IAEA,OAAO;MACLH,SAAS,CAACG,OAAV,GAAoB,KAApB;IACD,CAFD;EAGD,CAND,EAMG,EANH;wBAQyBC,QAAA,CAAe,CAAf;IAAhBC,YAAA,GAAAC,eAAA;EACT,IAAMC,QAAQ,GAAGX,MAAA,CAAkC;IACjD5B,MAAM,EAAEwC,SAAS,CAACd,KAAK,CAACC,aAAP,CADgC;IAEjDvB,MAAM,EAAEoC,SAAS,CAACd,KAAK,CAACG,aAAP,CAAT,IAAkCnB,WAFO;IAGjDR,OAAO,EAAEsC,SAAS,CAACd,KAAK,CAACI,cAAP,CAAT,IAAmCnB,YAHK;IAIjDN,MAAM,EAAEmC,SAAS,CAACd,KAAK,CAACK,aAAP,CAJgC;IAKjDzB,YAAY,EAAE,KALmC;IAMjDC,YAAY,EAAE,KANmC;IAOjDE,WAAW,EAAE;EAPoC,CAAlC,CAAjB;EAUA,IAAMb,KAAK,GAAG2C,QAAQ,CAACJ,OAAvB;EAEA,IAAMM,QAAQ,GAAGC,WAAA,CAAkB,UAACC,MAAD;IACjC,IAAMC,IAAI,GAAGL,QAAQ,CAACJ,OAAtB;IAEAI,QAAQ,CAACJ,OAAT,GAAmBxC,aAAa,CAACiD,IAAD,EAAOD,MAAP,CAAhC;;IAGA,IAAIC,IAAI,KAAKL,QAAQ,CAACJ,OAAtB,EAA+BE,YAAY,CAAC,UAAAQ,CAAC;MAAA,OAAIA,CAAC,GAAG,CAAR;IAAA,CAAF,CAAZ;EAChC,CAPgB,EAOd,EAPc,CAAjB;EASA,IAAMC,kBAAkB,GAAGJ,WAAA,CACzB,UAAC1C,MAAD,EAAiBQ,KAAjB;IACE,OAAO,IAAIuC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV;MACjB,IAAMC,mBAAmB,GAAIxB,KAAK,CAACyB,QAAN,CAAuBnD,MAAvB,EAA+BQ,KAA/B,CAA7B;MACA,IAAI0C,mBAAmB,IAAI,IAA3B,EAAiC;QAC/B;QACAF,OAAO,CAACtC,WAAD,CAAP;MACD,CAHD,MAGO,IAAIrD,SAAS,CAAC6F,mBAAD,CAAb,EAAoC;QACxCA,mBAAoC,CAAC5F,IAArC,CACC,UAAA8C,MAAM;UACJ4C,OAAO,CAAC5C,MAAM,IAAIM,WAAX,CAAP;QACD,CAHF,EAIC,UAAA0C,eAAe;UACb,IAAI3H,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;YACzC0H,OAAO,CAACC,IAAR,oFAEEF,eAFF;UAID;UAEDH,MAAM,CAACG,eAAD,CAAN;QACD,CAbF;MAeF,CAhBM,MAgBA;QACLJ,OAAO,CAACE,mBAAD,CAAP;MACD;IACF,CAxBM,CAAP;EAyBD,CA3BwB,EA4BzB,CAACxB,KAAK,CAACyB,QAAP,CA5ByB,CAA3B;EA+BA;;;;EAGA,IAAMI,mBAAmB,GAAGb,WAAA,CAC1B,UAAC1C,MAAD,EAAiBQ,KAAjB;IACE,IAAMgD,gBAAgB,GAAG9B,KAAK,CAAC8B,gBAA/B;IACA,IAAMC,MAAM,GAAGvH,UAAU,CAACsH,gBAAD,CAAV,GACXA,gBAAgB,CAAChD,KAAD,CADL,GAEXgD,gBAFJ;IAGA,IAAME,OAAO,GACXlD,KAAK,IAAIiD,MAAM,CAACE,UAAhB,GACIF,MAAM,CAACE,UAAP,CAAkBnD,KAAlB,EAAyBR,MAAzB,CADJ,GAEI4D,iBAAiB,CAAC5D,MAAD,EAASyD,MAAT,CAHvB;IAIA,OAAO,IAAIV,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV;MACjBS,OAAO,CAACpG,IAAR,CACE;QACE0F,OAAO,CAACtC,WAAD,CAAP;MACD,CAHH,EAIE,UAACmD,GAAD;QACE;QACA;QACA;QACA;QACA,IAAIA,GAAG,CAACC,IAAJ,KAAa,iBAAjB,EAAoC;UAClCd,OAAO,CAACe,eAAe,CAACF,GAAD,CAAhB,CAAP;QACD,CAFD,MAEO;UACL;UACA,IAAIpI,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;YACzC0H,OAAO,CAACC,IAAR,4FAEEO,GAFF;UAID;UAEDZ,MAAM,CAACY,GAAD,CAAN;QACD;MACF,CAtBH;IAwBD,CAzBM,CAAP;EA0BD,CApCyB,EAqC1B,CAACnC,KAAK,CAAC8B,gBAAP,CArC0B,CAA5B;EAwCA,IAAMQ,6BAA6B,GAAGtB,WAAA,CACpC,UAAClC,KAAD,EAAgB1E,KAAhB;IACE,OAAO,IAAIiH,OAAJ,CAAY,UAAAC,OAAO;MAAA,OACxBA,OAAO,CAACf,aAAa,CAACE,OAAd,CAAsB3B,KAAtB,EAA6B2C,QAA7B,CAAsCrH,KAAtC,CAAD,CADiB;IAAA,CAAnB,CAAP;EAGD,CALmC,EAMpC,EANoC,CAAtC;EASA,IAAMmI,wBAAwB,GAAGvB,WAAA,CAC/B,UAAC1C,MAAD;IACE,IAAMkE,uBAAuB,GAAavH,MAAM,CAAC2C,IAAP,CACxC2C,aAAa,CAACE,OAD0B,EAExCgC,MAFwC,CAEjC,UAAAC,CAAC;MAAA,OAAIlI,UAAU,CAAC+F,aAAa,CAACE,OAAd,CAAsBiC,CAAtB,EAAyBjB,QAA1B,CAAd;IAAA,CAFgC,CAA1C;;IAKA,IAAMkB,gBAAgB,GACpBH,uBAAuB,CAACjI,MAAxB,GAAiC,CAAjC,GACIiI,uBAAuB,CAACI,GAAxB,CAA4B,UAAAF,CAAC;MAAA,OAC3BJ,6BAA6B,CAACI,CAAD,EAAIrG,KAAK,CAACiC,MAAD,EAASoE,CAAT,CAAT,CADF;IAAA,CAA7B,CADJ,GAII,CAACrB,OAAO,CAACC,OAAR,CAAgB,iCAAhB,CAAD,CALN;;IAOA,OAAOD,OAAO,CAACwB,GAAR,CAAYF,gBAAZ,EAA8B/G,IAA9B,CAAmC,UAACkH,eAAD;MAAA,OACxCA,eAAe,CAACC,MAAhB,CAAuB,UAAC7B,IAAD,EAAO8B,IAAP,EAAaC,KAAb;QACrB,IAAID,IAAI,KAAK,iCAAb,EAAgD;UAC9C,OAAO9B,IAAP;QACD;QACD,IAAI8B,IAAJ,EAAU;UACR9B,IAAI,GAAGvE,KAAK,CAACuE,IAAD,EAAOsB,uBAAuB,CAACS,KAAD,CAA9B,EAAuCD,IAAvC,CAAZ;QACD;QACD,OAAO9B,IAAP;MACD,CARD,EAQG,EARH,CADwC;IAAA,CAAnC,CAAP;EAWD,CAzB8B,EA0B/B,CAACoB,6BAAD,CA1B+B,CAAjC;;EA8BA,IAAMY,iBAAiB,GAAGlC,WAAA,CACxB,UAAC1C,MAAD;IACE,OAAO+C,OAAO,CAACwB,GAAR,CAAY,CACjBN,wBAAwB,CAACjE,MAAD,CADP,EAEjB0B,KAAK,CAAC8B,gBAAN,GAAyBD,mBAAmB,CAACvD,MAAD,CAA5C,GAAuD,EAFtC,EAGjB0B,KAAK,CAACyB,QAAN,GAAiBL,kBAAkB,CAAC9C,MAAD,CAAnC,GAA8C,EAH7B,CAAZ,EAIJ1C,IAJI,CAIC,UAAAuH,KAAA;UAAEC,WAAA,GAAAD,KAAA;QAAaE,YAAA,GAAAF,KAAA;QAAcG,cAAA,GAAAH,KAAA;MACnC,IAAMI,cAAc,GAAGC,SAAS,CAACX,GAAV,CACrB,CAACO,WAAD,EAAcC,YAAd,EAA4BC,cAA5B,CADqB,EAErB;QAAEG,UAAU,EAAVA;MAAF,CAFqB,CAAvB;MAIA,OAAOF,cAAP;IACD,CAVM,CAAP;EAWD,CAbuB,EAcxB,CACEvD,KAAK,CAACyB,QADR,EAEEzB,KAAK,CAAC8B,gBAFR,EAGES,wBAHF,EAIEnB,kBAJF,EAKES,mBALF,CAdwB,CAA1B;;EAwBA,IAAM6B,4BAA4B,GAAGC,gBAAgB,CACnD,UAACrF,MAAD;QAACA,MAAA;MAAAA,MAAA,GAAiBJ,KAAK,CAACI,MAAA;;IACtByC,QAAQ,CAAC;MAAE3C,IAAI,EAAE,kBAAR;MAA4BG,OAAO,EAAE;IAArC,CAAD,CAAR;IACA,OAAO2E,iBAAiB,CAAC5E,MAAD,CAAjB,CAA0B1C,IAA1B,CAA+B,UAAA2H,cAAc;MAClD,IAAI,CAAC,CAACjD,SAAS,CAACG,OAAhB,EAAyB;QACvBM,QAAQ,CAAC;UAAE3C,IAAI,EAAE,kBAAR;UAA4BG,OAAO,EAAE;QAArC,CAAD,CAAR;QACAwC,QAAQ,CAAC;UAAE3C,IAAI,EAAE,YAAR;UAAsBG,OAAO,EAAEgF;QAA/B,CAAD,CAAR;MACD;MACD,OAAOA,cAAP;IACD,CANM,CAAP;EAOD,CAVkD,CAArD;EAaA/C,SAAA,CAAgB;IACd,IACEhB,eAAe,IACfc,SAAS,CAACG,OAAV,KAAsB,IADtB,IAEAhC,OAAO,CAACwB,aAAa,CAACQ,OAAf,EAAwBT,KAAK,CAACC,aAA9B,CAHT,EAIE;MACAyD,4BAA4B,CAACzD,aAAa,CAACQ,OAAf,CAA5B;IACD;EACF,CARD,EAQG,CAACjB,eAAD,EAAkBkE,4BAAlB,CARH;EAUA,IAAME,SAAS,GAAG5C,WAAA,CAChB,UAAC6C,SAAD;IACE,IAAMvF,MAAM,GACVuF,SAAS,IAAIA,SAAS,CAACvF,MAAvB,GACIuF,SAAS,CAACvF,MADd,GAEI2B,aAAa,CAACQ,OAHpB;IAIA,IAAM/B,MAAM,GACVmF,SAAS,IAAIA,SAAS,CAACnF,MAAvB,GACImF,SAAS,CAACnF,MADd,GAEIyB,aAAa,CAACM,OAAd,GACAN,aAAa,CAACM,OADd,GAEAT,KAAK,CAACG,aAAN,IAAuB,EAL7B;IAMA,IAAM3B,OAAO,GACXqF,SAAS,IAAIA,SAAS,CAACrF,OAAvB,GACIqF,SAAS,CAACrF,OADd,GAEI4B,cAAc,CAACK,OAAf,GACAL,cAAc,CAACK,OADf,GAEAT,KAAK,CAACI,cAAN,IAAwB,EAL9B;IAMA,IAAMzB,MAAM,GACVkF,SAAS,IAAIA,SAAS,CAAClF,MAAvB,GACIkF,SAAS,CAAClF,MADd,GAEI0B,aAAa,CAACI,OAAd,GACAJ,aAAa,CAACI,OADd,GAEAT,KAAK,CAACK,aALZ;IAMAJ,aAAa,CAACQ,OAAd,GAAwBnC,MAAxB;IACA6B,aAAa,CAACM,OAAd,GAAwB/B,MAAxB;IACA0B,cAAc,CAACK,OAAf,GAAyBjC,OAAzB;IACA6B,aAAa,CAACI,OAAd,GAAwB9B,MAAxB;IAEA,IAAMmF,UAAU,GAAG,SAAbA,UAAaA,CAAA;MACjB/C,QAAQ,CAAC;QACP3C,IAAI,EAAE,YADC;QAEPG,OAAO,EAAE;UACPK,YAAY,EAAE,CAAC,CAACiF,SAAF,IAAe,CAAC,CAACA,SAAS,CAACjF,YADlC;UAEPF,MAAM,EAANA,MAFO;UAGPF,OAAO,EAAPA,OAHO;UAIPG,MAAM,EAANA,MAJO;UAKPL,MAAM,EAANA,MALO;UAMPO,YAAY,EAAE,CAAC,CAACgF,SAAF,IAAe,CAAC,CAACA,SAAS,CAAChF,YANlC;UAOPE,WAAW,EACT,CAAC,CAAC8E,SAAF,IACA,CAAC,CAACA,SAAS,CAAC9E,WADZ,IAEA,OAAO8E,SAAS,CAAC9E,WAAjB,KAAiC,QAFjC,GAGI8E,SAAS,CAAC9E,WAHd,GAII;QAZC;MAFF,CAAD,CAAR;IAiBD,CAlBD;IAoBA,IAAIiB,KAAK,CAAC+D,OAAV,EAAmB;MACjB,IAAMC,oBAAoB,GAAIhE,KAAK,CAAC+D,OAAN,CAC5B7F,KAAK,CAACI,MADsB,EAE5B2F,iBAF4B,CAA9B;MAKA,IAAItI,SAAS,CAACqI,oBAAD,CAAb,EAAqC;QAClCA,oBAAqC,CAACpI,IAAtC,CAA2CkI,UAA3C;MACF,CAFD,MAEO;QACLA,UAAU;MACX;IACF,CAXD,MAWO;MACLA,UAAU;IACX;EACF,CA/De,EAgEhB,CAAC9D,KAAK,CAACG,aAAP,EAAsBH,KAAK,CAACK,aAA5B,EAA2CL,KAAK,CAACI,cAAjD,EAAiEJ,KAAK,CAAC+D,OAAvE,CAhEgB,CAAlB;EAmEAvD,SAAA,CAAgB;IACd,IACEF,SAAS,CAACG,OAAV,KAAsB,IAAtB,IACA,CAAChC,OAAO,CAACwB,aAAa,CAACQ,OAAf,EAAwBT,KAAK,CAACC,aAA9B,CAFV,EAGE;MACA,IAAIN,kBAAJ,EAAwB;QACtBM,aAAa,CAACQ,OAAd,GAAwBT,KAAK,CAACC,aAA9B;QACA2D,SAAS;QACT,IAAIpE,eAAJ,EAAqB;UACnBkE,4BAA4B,CAACzD,aAAa,CAACQ,OAAf,CAA5B;QACD;MACF;IACF;EACF,CAbD,EAaG,CACDd,kBADC,EAEDK,KAAK,CAACC,aAFL,EAGD2D,SAHC,EAIDpE,eAJC,EAKDkE,4BALC,CAbH;EAqBAlD,SAAA,CAAgB;IACd,IACEb,kBAAkB,IAClBW,SAAS,CAACG,OAAV,KAAsB,IADtB,IAEA,CAAChC,OAAO,CAAC0B,aAAa,CAACM,OAAf,EAAwBT,KAAK,CAACG,aAA9B,CAHV,EAIE;MACAA,aAAa,CAACM,OAAd,GAAwBT,KAAK,CAACG,aAAN,IAAuBnB,WAA/C;MACA+B,QAAQ,CAAC;QACP3C,IAAI,EAAE,YADC;QAEPG,OAAO,EAAEyB,KAAK,CAACG,aAAN,IAAuBnB;MAFzB,CAAD,CAAR;IAID;EACF,CAZD,EAYG,CAACW,kBAAD,EAAqBK,KAAK,CAACG,aAA3B,CAZH;EAcAK,SAAA,CAAgB;IACd,IACEb,kBAAkB,IAClBW,SAAS,CAACG,OAAV,KAAsB,IADtB,IAEA,CAAChC,OAAO,CAAC2B,cAAc,CAACK,OAAhB,EAAyBT,KAAK,CAACI,cAA/B,CAHV,EAIE;MACAA,cAAc,CAACK,OAAf,GAAyBT,KAAK,CAACI,cAAN,IAAwBnB,YAAjD;MACA8B,QAAQ,CAAC;QACP3C,IAAI,EAAE,aADC;QAEPG,OAAO,EAAEyB,KAAK,CAACI,cAAN,IAAwBnB;MAF1B,CAAD,CAAR;IAID;EACF,CAZD,EAYG,CAACU,kBAAD,EAAqBK,KAAK,CAACI,cAA3B,CAZH;EAcAI,SAAA,CAAgB;IACd,IACEb,kBAAkB,IAClBW,SAAS,CAACG,OAAV,KAAsB,IADtB,IAEA,CAAChC,OAAO,CAAC4B,aAAa,CAACI,OAAf,EAAwBT,KAAK,CAACK,aAA9B,CAHV,EAIE;MACAA,aAAa,CAACI,OAAd,GAAwBT,KAAK,CAACK,aAA9B;MACAU,QAAQ,CAAC;QACP3C,IAAI,EAAE,YADC;QAEPG,OAAO,EAAEyB,KAAK,CAACK;MAFR,CAAD,CAAR;IAID;EACF,CAZD,EAYG,CAACV,kBAAD,EAAqBK,KAAK,CAACK,aAA3B,EAA0CL,KAAK,CAACI,cAAhD,CAZH;EAcA,IAAM8D,aAAa,GAAGP,gBAAgB,CAAC,UAACvB,IAAD;IACrC;IACA;IACA;IAEA,IACE7B,aAAa,CAACE,OAAd,CAAsB2B,IAAtB,KACA5H,UAAU,CAAC+F,aAAa,CAACE,OAAd,CAAsB2B,IAAtB,EAA4BX,QAA7B,CAFZ,EAGE;MACA,IAAMrH,KAAK,GAAGiC,KAAK,CAAC6B,KAAK,CAACI,MAAP,EAAe8D,IAAf,CAAnB;MACA,IAAM+B,YAAY,GAAG5D,aAAa,CAACE,OAAd,CAAsB2B,IAAtB,EAA4BX,QAA5B,CAAqCrH,KAArC,CAArB;MACA,IAAIuB,SAAS,CAACwI,YAAD,CAAb,EAA6B;QAC3B;QACApD,QAAQ,CAAC;UAAE3C,IAAI,EAAE,kBAAR;UAA4BG,OAAO,EAAE;QAArC,CAAD,CAAR;QACA,OAAO4F,YAAY,CAChBvI,IADI,CACC,UAACuF,CAAD;UAAA,OAAYA,CAAZ;QAAA,CADD,EAEJvF,IAFI,CAEC,UAACwI,KAAD;UACJrD,QAAQ,CAAC;YACP3C,IAAI,EAAE,iBADC;YAEPG,OAAO,EAAE;cAAEO,KAAK,EAAEsD,IAAT;cAAehI,KAAK,EAAEgK;YAAtB;UAFF,CAAD,CAAR;UAIArD,QAAQ,CAAC;YAAE3C,IAAI,EAAE,kBAAR;YAA4BG,OAAO,EAAE;UAArC,CAAD,CAAR;QACD,CARI,CAAP;MASD,CAZD,MAYO;QACLwC,QAAQ,CAAC;UACP3C,IAAI,EAAE,iBADC;UAEPG,OAAO,EAAE;YACPO,KAAK,EAAEsD,IADA;YAEPhI,KAAK,EAAE+J;UAFA;QAFF,CAAD,CAAR;QAOA,OAAO9C,OAAO,CAACC,OAAR,CAAgB6C,YAAhB,CAAP;MACD;IACF,CA5BD,MA4BO,IAAInE,KAAK,CAAC8B,gBAAV,EAA4B;MACjCf,QAAQ,CAAC;QAAE3C,IAAI,EAAE,kBAAR;QAA4BG,OAAO,EAAE;MAArC,CAAD,CAAR;MACA,OAAOsD,mBAAmB,CAAC3D,KAAK,CAACI,MAAP,EAAe8D,IAAf,CAAnB,CACJxG,IADI,CACC,UAACuF,CAAD;QAAA,OAAYA,CAAZ;MAAA,CADD,EAEJvF,IAFI,CAEC,UAACwI,KAAD;QACJrD,QAAQ,CAAC;UACP3C,IAAI,EAAE,iBADC;UAEPG,OAAO,EAAE;YAAEO,KAAK,EAAEsD,IAAT;YAAehI,KAAK,EAAEiC,KAAK,CAAC+H,KAAD,EAAQhC,IAAR;UAA3B;QAFF,CAAD,CAAR;QAIArB,QAAQ,CAAC;UAAE3C,IAAI,EAAE,kBAAR;UAA4BG,OAAO,EAAE;QAArC,CAAD,CAAR;MACD,CARI,CAAP;IASD;IAED,OAAO8C,OAAO,CAACC,OAAR,EAAP;EACD,CA/CqC,CAAtC;EAiDA,IAAM+C,aAAa,GAAGrD,WAAA,CAAkB,UAACoB,IAAD,EAAAkC,KAAA;QAAiB7C,QAAA,GAAA6C,KAAA,CAAA7C,QAAA;IACvDlB,aAAa,CAACE,OAAd,CAAsB2B,IAAtB,IAA8B;MAC5BX,QAAQ,EAARA;IAD4B,CAA9B;EAGD,CAJqB,EAInB,EAJmB,CAAtB;EAMA,IAAM8C,eAAe,GAAGvD,WAAA,CAAkB,UAACoB,IAAD;IACxC,OAAO7B,aAAa,CAACE,OAAd,CAAsB2B,IAAtB,CAAP;EACD,CAFuB,EAErB,EAFqB,CAAxB;EAIA,IAAMoC,UAAU,GAAGb,gBAAgB,CACjC,UAACnF,OAAD,EAAiCiG,cAAjC;IACE1D,QAAQ,CAAC;MAAE3C,IAAI,EAAE,aAAR;MAAuBG,OAAO,EAAEC;IAAhC,CAAD,CAAR;IACA,IAAMkG,YAAY,GAChBD,cAAc,KAAKnL,SAAnB,GAA+BgG,cAA/B,GAAgDmF,cADlD;IAEA,OAAOC,YAAY,GACfhB,4BAA4B,CAACxF,KAAK,CAACI,MAAP,CADb,GAEf+C,OAAO,CAACC,OAAR,EAFJ;EAGD,CARgC,CAAnC;EAWA,IAAMqD,SAAS,GAAG3D,WAAA,CAAkB,UAACtC,MAAD;IAClCqC,QAAQ,CAAC;MAAE3C,IAAI,EAAE,YAAR;MAAsBG,OAAO,EAAEG;IAA/B,CAAD,CAAR;EACD,CAFiB,EAEf,EAFe,CAAlB;EAIA,IAAMkG,SAAS,GAAGjB,gBAAgB,CAChC,UAACrF,MAAD,EAAuCmG,cAAvC;IACE,IAAMI,cAAc,GAAGrK,UAAU,CAAC8D,MAAD,CAAV,GAAqBA,MAAM,CAACJ,KAAK,CAACI,MAAP,CAA3B,GAA4CA,MAAnE;IAEAyC,QAAQ,CAAC;MAAE3C,IAAI,EAAE,YAAR;MAAsBG,OAAO,EAAEsG;IAA/B,CAAD,CAAR;IACA,IAAMH,YAAY,GAChBD,cAAc,KAAKnL,SAAnB,GAA+B8F,gBAA/B,GAAkDqF,cADpD;IAEA,OAAOC,YAAY,GACfhB,4BAA4B,CAACmB,cAAD,CADb,GAEfxD,OAAO,CAACC,OAAR,EAFJ;EAGD,CAV+B,CAAlC;EAaA,IAAMwD,aAAa,GAAG9D,WAAA,CACpB,UAAClC,KAAD,EAAgB1E,KAAhB;IACE2G,QAAQ,CAAC;MACP3C,IAAI,EAAE,iBADC;MAEPG,OAAO,EAAE;QAAEO,KAAK,EAALA,KAAF;QAAS1E,KAAK,EAALA;MAAT;IAFF,CAAD,CAAR;EAID,CANmB,EAOpB,EAPoB,CAAtB;EAUA,IAAM2K,aAAa,GAAGpB,gBAAgB,CACpC,UAAC7E,KAAD,EAAgB1E,KAAhB,EAA4BqK,cAA5B;IACE1D,QAAQ,CAAC;MACP3C,IAAI,EAAE,iBADC;MAEPG,OAAO,EAAE;QACPO,KAAK,EAALA,KADO;QAEP1E,KAAK,EAALA;MAFO;IAFF,CAAD,CAAR;IAOA,IAAMsK,YAAY,GAChBD,cAAc,KAAKnL,SAAnB,GAA+B8F,gBAA/B,GAAkDqF,cADpD;IAEA,OAAOC,YAAY,GACfhB,4BAA4B,CAAC/G,KAAK,CAACuB,KAAK,CAACI,MAAP,EAAeQ,KAAf,EAAsB1E,KAAtB,CAAN,CADb,GAEfiH,OAAO,CAACC,OAAR,EAFJ;EAGD,CAdmC,CAAtC;EAiBA,IAAM0D,aAAa,GAAGhE,WAAA,CACpB,UAACiE,gBAAD,EAAoDC,SAApD;IACE;IACA;IACA;IACA,IAAIpG,KAAK,GAAGoG,SAAZ;IACA,IAAIpH,GAAG,GAAGmH,gBAAV;IACA,IAAIE,MAAJ;IAEA;;IACA,IAAI,CAACnK,QAAQ,CAACiK,gBAAD,CAAb,EAAiC;MAC/B;MACA;MACA,IAAKA,gBAAwB,CAACG,OAA9B,EAAuC;QACpCH,gBAA2C,CAACG,OAA5C;MACF;MACD,IAAMtJ,MAAM,GAAGmJ,gBAAgB,CAACnJ,MAAjB,GACVmJ,gBAA2C,CAACnJ,MADlC,GAEVmJ,gBAA2C,CAACI,aAFjD;MAN+B,IAW7BjH,IAX6B,GAmB3BtC,MAnB2B,CAW7BsC,IAX6B;QAY7BgE,IAZ6B,GAmB3BtG,MAnB2B,CAY7BsG,IAZ6B;QAa7BkD,EAb6B,GAmB3BxJ,MAnB2B,CAa7BwJ,EAb6B;QAc7BlL,KAd6B,GAmB3B0B,MAnB2B,CAc7B1B,KAd6B;QAe7BmL,OAf6B,GAmB3BzJ,MAnB2B,CAe7ByJ,OAf6B;QAgB7BC,SAhB6B,GAmB3B1J,MAnB2B,CAgB7B0J,SAhB6B;QAiB7BC,OAjB6B,GAmB3B3J,MAnB2B,CAiB7B2J,OAjB6B;QAkB7BC,QAlB6B,GAmB3B5J,MAnB2B,CAkB7B4J,QAlB6B;MAqB/B5G,KAAK,GAAGoG,SAAS,GAAGA,SAAH,GAAe9C,IAAI,GAAGA,IAAH,GAAUkD,EAA9C;MACA,IAAI,CAACxG,KAAD,IAAA/E,OAAA,CAAAC,GAAA,CAAAC,QAAA,iBAAJ,EAAuB;QACrB0L,0BAA0B,CAAC;UACzBC,WAAW,EAAEJ,SADY;UAEzBK,uBAAuB,EAAE,0CAFA;UAGzBC,WAAW,EAAE;QAHY,CAAD,CAA1B;MAKD;MACDhI,GAAG,GAAG,eAAeiI,IAAf,CAAoB3H,IAApB,KACA+G,MAAM,GAAGa,UAAU,CAAC5L,KAAD,CAApB,EAA8BkB,KAAK,CAAC6J,MAAD,CAAL,GAAgB,EAAhB,GAAqBA,MADlD,IAEF,WAAWY,IAAX,CAAgB3H,IAAhB;MAAA,EACA6H,mBAAmB,CAAC5J,KAAK,CAAC6B,KAAK,CAACI,MAAP,EAAeQ,KAAf,CAAN,EAA8ByG,OAA9B,EAAuCnL,KAAvC,CADnB,GAEAqL,OAAO,IAAIC,QAAX;MAAA,EACAQ,iBAAiB,CAACT,OAAD,CADjB,GAEArL,KANJ;IAOD;IAED,IAAI0E,KAAJ,EAAW;MACT;MACAiG,aAAa,CAACjG,KAAD,EAAQhB,GAAR,CAAb;IACD;EACF,CApDmB,EAqDpB,CAACiH,aAAD,EAAgB7G,KAAK,CAACI,MAAtB,CArDoB,CAAtB;EAwDA,IAAM6H,YAAY,GAAGxC,gBAAgB,CACnC,UACEyC,WADF;IAGE,IAAIpL,QAAQ,CAACoL,WAAD,CAAZ,EAA2B;MACzB,OAAO,UAAAC,KAAK;QAAA,OAAIrB,aAAa,CAACqB,KAAD,EAAQD,WAAR,CAAjB;MAAA,CAAZ;IACD,CAFD,MAEO;MACLpB,aAAa,CAACoB,WAAD,CAAb;IACD;EACF,CATkC,CAArC;EAYA,IAAME,eAAe,GAAG3C,gBAAgB,CACtC,UAAC7E,KAAD,EAAgBN,OAAhB,EAAyCiG,cAAzC;QAAgBjG,OAAA;MAAAA,OAAA,GAAmB;;IACjCuC,QAAQ,CAAC;MACP3C,IAAI,EAAE,mBADC;MAEPG,OAAO,EAAE;QACPO,KAAK,EAALA,KADO;QAEP1E,KAAK,EAAEoE;MAFA;IAFF,CAAD,CAAR;IAOA,IAAMkG,YAAY,GAChBD,cAAc,KAAKnL,SAAnB,GAA+BgG,cAA/B,GAAgDmF,cADlD;IAEA,OAAOC,YAAY,GACfhB,4BAA4B,CAACxF,KAAK,CAACI,MAAP,CADb,GAEf+C,OAAO,CAACC,OAAR,EAFJ;EAGD,CAdqC,CAAxC;EAiBA,IAAMiF,WAAW,GAAGvF,WAAA,CAClB,UAAC5E,CAAD,EAASK,IAAT;IACE,IAAIL,CAAC,CAACgJ,OAAN,EAAe;MACbhJ,CAAC,CAACgJ,OAAF;IACD;oBAC+BhJ,CAAC,CAACN,MAAA;MAA1BsG,IAAA,GAAAoE,SAAA,CAAApE,IAAA;MAAMkD,EAAA,GAAAkB,SAAA,CAAAlB,EAAA;MAAIE,SAAA,GAAAgB,SAAA,CAAAhB,SAAA;IAClB,IAAM1G,KAAK,GAAGrC,IAAI,GAAGA,IAAH,GAAU2F,IAAI,GAAGA,IAAH,GAAUkD,EAA1C;IAEA,IAAI,CAACxG,KAAD,IAAA/E,OAAA,CAAAC,GAAA,CAAAC,QAAA,iBAAJ,EAAuB;MACrB0L,0BAA0B,CAAC;QACzBC,WAAW,EAAEJ,SADY;QAEzBK,uBAAuB,EAAE,wBAFA;QAGzBC,WAAW,EAAE;MAHY,CAAD,CAA1B;IAKD;IAEDQ,eAAe,CAACxH,KAAD,EAAQ,IAAR,CAAf;EACD,CAjBiB,EAkBlB,CAACwH,eAAD,CAlBkB,CAApB;EAqBA,IAAMG,UAAU,GAAG9C,gBAAgB,CACjC,UAAC+C,aAAD;IACE,IAAI1L,QAAQ,CAAC0L,aAAD,CAAZ,EAA6B;MAC3B,OAAO,UAAAL,KAAK;QAAA,OAAIE,WAAW,CAACF,KAAD,EAAQK,aAAR,CAAf;MAAA,CAAZ;IACD,CAFD,MAEO;MACLH,WAAW,CAACG,aAAD,CAAX;IACD;EACF,CAPgC,CAAnC;EAUA,IAAMC,cAAc,GAAG3F,WAAA,CACrB,UACE4F,SADF;IAKE,IAAIpM,UAAU,CAACoM,SAAD,CAAd,EAA2B;MACzB7F,QAAQ,CAAC;QAAE3C,IAAI,EAAE,kBAAR;QAA4BG,OAAO,EAAEqI;MAArC,CAAD,CAAR;IACD,CAFD,MAEO;MACL7F,QAAQ,CAAC;QAAE3C,IAAI,EAAE,kBAAR;QAA4BG,OAAO,EAAE,SAAAA,QAAA;UAAA,OAAMqI,SAAN;QAAA;MAArC,CAAD,CAAR;IACD;EACF,CAXoB,EAYrB,EAZqB,CAAvB;EAeA,IAAMC,SAAS,GAAG7F,WAAA,CAAkB,UAACrC,MAAD;IAClCoC,QAAQ,CAAC;MAAE3C,IAAI,EAAE,YAAR;MAAsBG,OAAO,EAAEI;IAA/B,CAAD,CAAR;EACD,CAFiB,EAEf,EAFe,CAAlB;EAIA,IAAMmI,aAAa,GAAG9F,WAAA,CAAkB,UAACpC,YAAD;IACtCmC,QAAQ,CAAC;MAAE3C,IAAI,EAAE,kBAAR;MAA4BG,OAAO,EAAEK;IAArC,CAAD,CAAR;EACD,CAFqB,EAEnB,EAFmB,CAAtB;EAIA,IAAMmI,UAAU,GAAGpD,gBAAgB,CAAC;IAClC5C,QAAQ,CAAC;MAAE3C,IAAI,EAAE;IAAR,CAAD,CAAR;IACA,OAAOsF,4BAA4B,GAAG9H,IAA/B,CACL,UAAC2H,cAAD;MACE;MACA;MACA;MACA;MACA;MACA;MAEA,IAAMyD,iBAAiB,GAAGzD,cAAc,YAAY0D,KAApD;MACA,IAAMC,eAAe,GACnB,CAACF,iBAAD,IAAsB/L,MAAM,CAAC2C,IAAP,CAAY2F,cAAZ,EAA4BhJ,MAA5B,KAAuC,CAD/D;MAEA,IAAI2M,eAAJ,EAAqB;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIC,kBAAJ;QACA,IAAI;UACFA,kBAAkB,GAAGC,aAAa,EAAlC,CADE;UAGF;;UACA,IAAID,kBAAkB,KAAK7N,SAA3B,EAAsC;YACpC;UACD;QACF,CAPD,CAOE,OAAO8K,KAAP,EAAc;UACd,MAAMA,KAAN;QACD;QAED,OAAO/C,OAAO,CAACC,OAAR,CAAgB6F,kBAAhB,EACJvL,IADI,CACC,UAAAyL,MAAM;UACV,IAAI,CAAC,CAAC/G,SAAS,CAACG,OAAhB,EAAyB;YACvBM,QAAQ,CAAC;cAAE3C,IAAI,EAAE;YAAR,CAAD,CAAR;UACD;UACD,OAAOiJ,MAAP;QACD,CANI,WAOE,UAAAC,OAAO;UACZ,IAAI,CAAC,CAAChH,SAAS,CAACG,OAAhB,EAAyB;YACvBM,QAAQ,CAAC;cAAE3C,IAAI,EAAE;YAAR,CAAD,CAAR,CADuB;YAGvB;;YACA,MAAMkJ,OAAN;UACD;QACF,CAdI,CAAP;MAeD,CAtCD,MAsCO,IAAI,CAAC,CAAChH,SAAS,CAACG,OAAhB,EAAyB;QAC9B;QACAM,QAAQ,CAAC;UAAE3C,IAAI,EAAE;QAAR,CAAD,CAAR,CAF8B;;QAI9B,IAAI4I,iBAAJ,EAAuB;UACrB,MAAMzD,cAAN;QACD;MACF;MACD;IACD,CA3DI,CAAP;EA6DD,CA/DkC,CAAnC;EAiEA,IAAMgE,YAAY,GAAG5D,gBAAgB,CACnC,UAACvH,CAAD;IACE,IAAIA,CAAC,IAAIA,CAAC,CAACoL,cAAP,IAAyBhN,UAAU,CAAC4B,CAAC,CAACoL,cAAH,CAAvC,EAA2D;MACzDpL,CAAC,CAACoL,cAAF;IACD;IAED,IAAIpL,CAAC,IAAIA,CAAC,CAACqL,eAAP,IAA0BjN,UAAU,CAAC4B,CAAC,CAACqL,eAAH,CAAxC,EAA6D;MAC3DrL,CAAC,CAACqL,eAAF;IACD;IAGD;IACA;IACA;;IACA,IAAI1N,OAAA,CAAAC,GAAA,CAAAC,QAAA,qBAAW,OAAOgC,QAAP,KAAoB,WAAnC,EAAgD;MAC9C;MACA,IAAMC,aAAa,GAAGH,gBAAgB,EAAtC;MACA,IACEG,aAAa,KAAK,IAAlB,IACAA,aAAa,YAAYwL,iBAF3B,EAGE;QACA,EACExL,aAAa,CAACyL,UAAd,IACEzL,aAAa,CAACyL,UAAd,CAAyBC,YAAzB,CAAsC,MAAtC,CAFJ,IAAA7N,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAGP,yMAHO,CAAT,GAAAA,SAAS,OAAT;MAKD;IACF;IAED6M,UAAU,WAAV,CAAmB,UAAAc,MAAM;MACvBlG,OAAO,CAACC,IAAR,6DAEEiG,MAFF;IAID,CALD;EAMD,CAnCkC,CAArC;EAsCA,IAAM5D,iBAAiB,GAA0B;IAC/CL,SAAS,EAATA,SAD+C;IAE/CkE,YAAY,EAAEpE,4BAFiC;IAG/CQ,aAAa,EAAbA,aAH+C;IAI/CS,SAAS,EAATA,SAJ+C;IAK/CG,aAAa,EAAbA,aAL+C;IAM/CwB,eAAe,EAAfA,eAN+C;IAO/CvB,aAAa,EAAbA,aAP+C;IAQ/C8B,SAAS,EAATA,SAR+C;IAS/CC,aAAa,EAAbA,aAT+C;IAU/CtC,UAAU,EAAVA,UAV+C;IAW/CI,SAAS,EAATA,SAX+C;IAY/C+B,cAAc,EAAdA,cAZ+C;IAa/CI,UAAU,EAAVA;EAb+C,CAAjD;EAgBA,IAAMK,aAAa,GAAGzD,gBAAgB,CAAC;IACrC,OAAO9D,QAAQ,CAAC3B,KAAK,CAACI,MAAP,EAAe2F,iBAAf,CAAf;EACD,CAFqC,CAAtC;EAIA,IAAM8D,WAAW,GAAGpE,gBAAgB,CAAC,UAAAvH,CAAC;IACpC,IAAIA,CAAC,IAAIA,CAAC,CAACoL,cAAP,IAAyBhN,UAAU,CAAC4B,CAAC,CAACoL,cAAH,CAAvC,EAA2D;MACzDpL,CAAC,CAACoL,cAAF;IACD;IAED,IAAIpL,CAAC,IAAIA,CAAC,CAACqL,eAAP,IAA0BjN,UAAU,CAAC4B,CAAC,CAACqL,eAAH,CAAxC,EAA6D;MAC3DrL,CAAC,CAACqL,eAAF;IACD;IAED7D,SAAS;EACV,CAVmC,CAApC;EAYA,IAAMoE,YAAY,GAAGhH,WAAA,CACnB,UAACoB,IAAD;IACE,OAAO;MACLhI,KAAK,EAAEiC,KAAK,CAAC6B,KAAK,CAACI,MAAP,EAAe8D,IAAf,CADP;MAELgC,KAAK,EAAE/H,KAAK,CAAC6B,KAAK,CAACQ,MAAP,EAAe0D,IAAf,CAFP;MAGL5D,OAAO,EAAE,CAAC,CAACnC,KAAK,CAAC6B,KAAK,CAACM,OAAP,EAAgB4D,IAAhB,CAHX;MAIL6F,YAAY,EAAE5L,KAAK,CAAC4D,aAAa,CAACQ,OAAf,EAAwB2B,IAAxB,CAJd;MAKLhC,cAAc,EAAE,CAAC,CAAC/D,KAAK,CAAC+D,cAAc,CAACK,OAAhB,EAAyB2B,IAAzB,CALlB;MAML8F,YAAY,EAAE7L,KAAK,CAAC8D,aAAa,CAACM,OAAf,EAAwB2B,IAAxB;IANd,CAAP;EAQD,CAVkB,EAWnB,CAAClE,KAAK,CAACQ,MAAP,EAAeR,KAAK,CAACM,OAArB,EAA8BN,KAAK,CAACI,MAApC,CAXmB,CAArB;EAcA,IAAM6J,eAAe,GAAGnH,WAAA,CACtB,UAACoB,IAAD;IACE,OAAO;MACLgG,QAAQ,EAAE,SAAAA,SAAChO,KAAD,EAAaqK,cAAb;QAAA,OACRM,aAAa,CAAC3C,IAAD,EAAOhI,KAAP,EAAcqK,cAAd,CADL;MAAA,CADL;MAGLD,UAAU,EAAE,SAAAA,WAACpK,KAAD,EAAiBqK,cAAjB;QAAA,OACV6B,eAAe,CAAClE,IAAD,EAAOhI,KAAP,EAAcqK,cAAd,CADL;MAAA,CAHP;MAKL4D,QAAQ,EAAE,SAAAA,SAACjO,KAAD;QAAA,OAAgB0K,aAAa,CAAC1C,IAAD,EAAOhI,KAAP,CAA7B;MAAA;IALL,CAAP;EAOD,CATqB,EAUtB,CAAC2K,aAAD,EAAgBuB,eAAhB,EAAiCxB,aAAjC,CAVsB,CAAxB;EAaA,IAAMwD,aAAa,GAAGtH,WAAA,CACpB,UAACuH,aAAD;IACE,IAAMC,UAAU,GAAG9N,QAAQ,CAAC6N,aAAD,CAA3B;IACA,IAAMnG,IAAI,GAAGoG,UAAU,GAClBD,aAAkC,CAACnG,IADjB,GAEnBmG,aAFJ;IAGA,IAAME,UAAU,GAAGpM,KAAK,CAAC6B,KAAK,CAACI,MAAP,EAAe8D,IAAf,CAAxB;IAEA,IAAMtD,KAAK,GAAyB;MAClCsD,IAAI,EAAJA,IADkC;MAElChI,KAAK,EAAEqO,UAF2B;MAGlCC,QAAQ,EAAEvC,YAHwB;MAIlCwC,MAAM,EAAElC;IAJ0B,CAApC;IAMA,IAAI+B,UAAJ,EAAgB;MAAA,IAEZpK,IAFY,GAMVmK,aANU,CAEZnK,IAFY;QAGLwK,SAHK,GAMVL,aANU,CAGZnO,KAHY;QAIRyO,EAJQ,GAMVN,aANU,CAIZO,EAJY;QAKZpD,QALY,GAMV6C,aANU,CAKZ7C,QALY;MAQd,IAAItH,IAAI,KAAK,UAAb,EAAyB;QACvB,IAAIwK,SAAS,KAAKtP,SAAlB,EAA6B;UAC3BwF,KAAK,CAACyG,OAAN,GAAgB,CAAC,CAACkD,UAAlB;QACD,CAFD,MAEO;UACL3J,KAAK,CAACyG,OAAN,GAAgB,CAAC,EACflL,KAAK,CAACC,OAAN,CAAcmO,UAAd,KAA6B,CAACA,UAAU,CAACM,OAAX,CAAmBH,SAAnB,CADf,CAAjB;UAGA9J,KAAK,CAAC1E,KAAN,GAAcwO,SAAd;QACD;MACF,CATD,MASO,IAAIxK,IAAI,KAAK,OAAb,EAAsB;QAC3BU,KAAK,CAACyG,OAAN,GAAgBkD,UAAU,KAAKG,SAA/B;QACA9J,KAAK,CAAC1E,KAAN,GAAcwO,SAAd;MACD,CAHM,MAGA,IAAIC,EAAE,KAAK,QAAP,IAAmBnD,QAAvB,EAAiC;QACtC5G,KAAK,CAAC1E,KAAN,GAAc0E,KAAK,CAAC1E,KAAN,IAAe,EAA7B;QACA0E,KAAK,CAAC4G,QAAN,GAAiB,IAAjB;MACD;IACF;IACD,OAAO5G,KAAP;EACD,CAxCmB,EAyCpB,CAAC2H,UAAD,EAAaN,YAAb,EAA2BjI,KAAK,CAACI,MAAjC,CAzCoB,CAAtB;EA4CA,IAAM0K,KAAK,GAAGC,OAAA,CACZ;IAAA,OAAM,CAACxK,OAAO,CAACwB,aAAa,CAACQ,OAAf,EAAwBvC,KAAK,CAACI,MAA9B,CAAd;EAAA,CADY,EAEZ,CAAC2B,aAAa,CAACQ,OAAf,EAAwBvC,KAAK,CAACI,MAA9B,CAFY,CAAd;EAKA,IAAM4K,OAAO,GAAGD,OAAA,CACd;IAAA,OACE,OAAOvJ,cAAP,KAA0B,WAA1B,GACIsJ,KAAK,GACH9K,KAAK,CAACQ,MAAN,IAAgBzD,MAAM,CAAC2C,IAAP,CAAYM,KAAK,CAACQ,MAAlB,EAA0BnE,MAA1B,KAAqC,CADlD,GAEHmF,cAAc,KAAK,KAAnB,IAA4BlF,UAAU,CAACkF,cAAD,CAAtC,GACCA,cAA2D,CAACM,KAAD,CAD5D,GAECN,cALP,GAMIxB,KAAK,CAACQ,MAAN,IAAgBzD,MAAM,CAAC2C,IAAP,CAAYM,KAAK,CAACQ,MAAlB,EAA0BnE,MAA1B,KAAqC,CAP3D;EAAA,CADc,EASd,CAACmF,cAAD,EAAiBsJ,KAAjB,EAAwB9K,KAAK,CAACQ,MAA9B,EAAsCsB,KAAtC,CATc,CAAhB;EAYA,IAAMmJ,GAAG,GAAA9K,QAAA,KACJH,KADI;IAEP+B,aAAa,EAAEA,aAAa,CAACQ,OAFtB;IAGPN,aAAa,EAAEA,aAAa,CAACM,OAHtB;IAIPL,cAAc,EAAEA,cAAc,CAACK,OAJxB;IAKPJ,aAAa,EAAEA,aAAa,CAACI,OALtB;IAMPgG,UAAU,EAAVA,UANO;IAOPN,YAAY,EAAZA,YAPO;IAQP4B,WAAW,EAAXA,WARO;IASPR,YAAY,EAAZA,YATO;IAUP3D,SAAS,EAATA,SAVO;IAWPe,SAAS,EAATA,SAXO;IAYPgC,cAAc,EAAdA,cAZO;IAaPL,eAAe,EAAfA,eAbO;IAcPvB,aAAa,EAAbA,aAdO;IAePD,aAAa,EAAbA,aAfO;IAgBP+B,SAAS,EAATA,SAhBO;IAiBPC,aAAa,EAAbA,aAjBO;IAkBPtC,UAAU,EAAVA,UAlBO;IAmBPI,SAAS,EAATA,SAnBO;IAoBPmC,UAAU,EAAVA,UApBO;IAqBPe,YAAY,EAAEpE,4BArBP;IAsBPQ,aAAa,EAAbA,aAtBO;IAuBPgF,OAAO,EAAPA,OAvBO;IAwBPF,KAAK,EAALA,KAxBO;IAyBPzE,eAAe,EAAfA,eAzBO;IA0BPF,aAAa,EAAbA,aA1BO;IA2BPiE,aAAa,EAAbA,aA3BO;IA4BPN,YAAY,EAAZA,YA5BO;IA6BPG,eAAe,EAAfA,eA7BO;IA8BP7I,cAAc,EAAdA,cA9BO;IA+BPF,gBAAgB,EAAhBA,gBA/BO;IAgCPI,eAAe,EAAfA;EAhCO,EAAT;EAmCA,OAAO2J,GAAP;AACD;AAED,SAAgBC,OAGdpJ,KAAA;EACA,IAAMqJ,SAAS,GAAGnK,SAAS,CAASc,KAAT,CAA3B;MACQsJ,SAAA,GAA0CtJ,KAAA,CAA1CsJ,SAAA;IAAW9N,QAAA,GAA+BwE,KAAA,CAA/BxE,QAAA;IAAU+N,MAAA,GAAqBvJ,KAAA,CAArBuJ,MAAA;IAAQC,QAAA,GAAaxJ,KAAA,CAAbwJ,QAAA;;EAGrCC,mBAAA,CAA0BD,QAA1B,EAAoC;IAAA,OAAMH,SAAN;EAAA,CAApC;EAEA,IAAAtP,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;IACX;IACAuG,SAAA,CAAgB;MACd,CACE,CAACR,KAAK,CAACuJ,MADT,GAAAxP,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,4PAAT,GAAAA,SAAS,OAAT;IAKD,CAND,EAMG,EANH;EAOD;EACD,OACEwP,aAAA,CAAClQ,cAAD;IAAgBY,KAAK,EAAEiP;GAAvB,EACGC,SAAS,GACNI,aAAA,CAAoBJ,SAApB,EAAsCD,SAAtC,CADM,GAENE,MAAM,GACNA,MAAM,CAACF,SAAD,CADA,GAEN7N,QAAQ;EAAA,EACRhB,UAAU,CAACgB,QAAD,CAAV,GACGA,QAA0D,CACzD6N,SADyD,CAD7D,GAIE,CAAC9N,eAAe,CAACC,QAAD,CAAhB,GACAC,QAAA,CAAekO,IAAf,CAAoBnO,QAApB,CADA,GAEA,IAPM,GAQR,IAbN,CADF;AAiBD;AAED,SAASmK,0BAATA,CAAAiE,KAAA;MACEhE,WAAA,GAAAgE,KAAA,CAAAhE,WAAA;IACAC,uBAAA,GAAA+D,KAAA,CAAA/D,uBAAA;IACAC,WAAA,GAAA8D,KAAA,CAAA9D,WAAA;EAMAnE,OAAO,CAACC,IAAR,8BAC8BkE,WAD9B,kFAEIF,WAFJ,kHAGwGC,uBAHxG;AAMD;AAED;;;;AAGA,SAAgBxD,gBAAwBwH,QAAA;EACtC,IAAInL,MAAM,GAAyB,EAAnC;EACA,IAAImL,QAAQ,CAACC,KAAb,EAAoB;IAClB,IAAID,QAAQ,CAACC,KAAT,CAAevP,MAAf,KAA0B,CAA9B,EAAiC;MAC/B,OAAOoC,KAAK,CAAC+B,MAAD,EAASmL,QAAQ,CAACpN,IAAlB,EAAwBoN,QAAQ,CAACE,OAAjC,CAAZ;IACD;IACD,SAAAC,SAAA,GAAgBH,QAAQ,CAACC,KAAzB,EAAAG,QAAA,GAAA5P,KAAA,CAAAC,OAAA,CAAA0P,SAAA,GAAAtM,EAAA,MAAAsM,SAAA,GAAAC,QAAA,GAAAD,SAAA,GAAAA,SAAA,CAAAE,MAAA,CAAAC,QAAA,OAAgC;MAAA,IAAAC,KAAA;MAAA,IAAAH,QAAA;QAAA,IAAAvM,EAAA,IAAAsM,SAAA,CAAAzP,MAAA;QAAA6P,KAAA,GAAAJ,SAAA,CAAAtM,EAAA;MAAA;QAAAA,EAAA,GAAAsM,SAAA,CAAAK,IAAA;QAAA,IAAA3M,EAAA,CAAA4M,IAAA;QAAAF,KAAA,GAAA1M,EAAA,CAAAtD,KAAA;MAAA;MAAA,IAAvB+H,GAAuB,GAAAiI,KAAA;MAC9B,IAAI,CAAC/N,KAAK,CAACqC,MAAD,EAASyD,GAAG,CAAC1F,IAAb,CAAV,EAA8B;QAC5BiC,MAAM,GAAG/B,KAAK,CAAC+B,MAAD,EAASyD,GAAG,CAAC1F,IAAb,EAAmB0F,GAAG,CAAC4H,OAAvB,CAAd;MACD;IACF;EACF;EACD,OAAOrL,MAAP;AACD;AAED;;;;AAGA,SAAgBwD,kBACd5D,MAAA,EACAyD,MAAA,EACAwI,IAAA,EACAC,OAAA;MADAD,IAAA;IAAAA,IAAA,GAAgB;;EAGhB,IAAME,gBAAgB,GAAiBC,wBAAwB,CAACpM,MAAD,CAA/D;EAEA,OAAOyD,MAAM,CAACwI,IAAI,GAAG,cAAH,GAAoB,UAAzB,CAAN,CAA2CE,gBAA3C,EAA6D;IAClEE,UAAU,EAAE,KADsD;IAElEH,OAAO,EAAEA,OAAO,IAAIC;EAF8C,CAA7D,CAAP;AAID;AAED;;;;AAGA,SAAgBC,yBACdpM,MAAA;EAEA,IAAIsM,IAAI,GAAiBvQ,KAAK,CAACC,OAAN,CAAcgE,MAAd,IAAwB,EAAxB,GAA6B,EAAtD;EACA,KAAK,IAAIT,CAAT,IAAcS,MAAd,EAAsB;IACpB,IAAIrD,MAAM,CAACC,SAAP,CAAiB2P,cAAjB,CAAgCzP,IAAhC,CAAqCkD,MAArC,EAA6CT,CAA7C,CAAJ,EAAqD;MACnD,IAAMvB,GAAG,GAAG1B,MAAM,CAACiD,CAAD,CAAlB;MACA,IAAIxD,KAAK,CAACC,OAAN,CAAcgE,MAAM,CAAChC,GAAD,CAApB,MAA+B,IAAnC,EAAyC;QACvCsO,IAAI,CAACtO,GAAD,CAAJ,GAAYgC,MAAM,CAAChC,GAAD,CAAN,CAAYsG,GAAZ,CAAgB,UAACxI,KAAD;UAC1B,IAAIC,KAAK,CAACC,OAAN,CAAcF,KAAd,MAAyB,IAAzB,IAAiC0Q,aAAa,CAAC1Q,KAAD,CAAlD,EAA2D;YACzD,OAAOsQ,wBAAwB,CAACtQ,KAAD,CAA/B;UACD,CAFD,MAEO;YACL,OAAOA,KAAK,KAAK,EAAV,GAAeA,KAAf,GAAuBd,SAA9B;UACD;QACF,CANW,CAAZ;MAOD,CARD,MAQO,IAAIwR,aAAa,CAACxM,MAAM,CAAChC,GAAD,CAAP,CAAjB,EAAgC;QACrCsO,IAAI,CAACtO,GAAD,CAAJ,GAAYoO,wBAAwB,CAACpM,MAAM,CAAChC,GAAD,CAAP,CAApC;MACD,CAFM,MAEA;QACLsO,IAAI,CAACtO,GAAD,CAAJ,GAAYgC,MAAM,CAAChC,GAAD,CAAN,KAAgB,EAAhB,GAAqBgC,MAAM,CAAChC,GAAD,CAA3B,GAAmChD,SAA/C;MACD;IACF;EACF;EACD,OAAOsR,IAAP;AACD;AAED;;;;;AAIA,SAASnH,UAATA,CAAoB3H,MAApB,EAAmCiP,MAAnC,EAAkDtF,OAAlD;EACE,IAAMuF,WAAW,GAAGlP,MAAM,CAACqB,KAAP,EAApB;EAEA4N,MAAM,CAACE,OAAP,CAAe,SAASC,KAATA,CAAe9O,CAAf,EAAuBW,CAAvB;IACb,IAAI,OAAOiO,WAAW,CAACjO,CAAD,CAAlB,KAA0B,WAA9B,EAA2C;MACzC,IAAMoO,cAAc,GAAG1F,OAAO,CAAC5I,KAAR,KAAkB,KAAzC;MACA,IAAMuO,WAAW,GAAGD,cAAc,IAAI1F,OAAO,CAAC4F,iBAAR,CAA0BjP,CAA1B,CAAtC;MACA4O,WAAW,CAACjO,CAAD,CAAX,GAAiBqO,WAAW,GACxB5H,SAAS,CAACnJ,KAAK,CAACC,OAAN,CAAc8B,CAAd,IAAmB,EAAnB,GAAwB,EAAzB,EAA6BA,CAA7B,EAAgCqJ,OAAhC,CADe,GAExBrJ,CAFJ;IAGD,CAND,MAMO,IAAIqJ,OAAO,CAAC4F,iBAAR,CAA0BjP,CAA1B,CAAJ,EAAkC;MACvC4O,WAAW,CAACjO,CAAD,CAAX,GAAiByG,SAAS,CAAC1H,MAAM,CAACiB,CAAD,CAAP,EAAYX,CAAZ,EAAeqJ,OAAf,CAA1B;IACD,CAFM,MAEA,IAAI3J,MAAM,CAACiN,OAAP,CAAe3M,CAAf,MAAsB,CAAC,CAA3B,EAA8B;MACnC4O,WAAW,CAACM,IAAZ,CAAiBlP,CAAjB;IACD;EACF,CAZD;EAaA,OAAO4O,WAAP;AACD;AAED;;AACA,SAAS9E,iBAATA,CAA2BT,OAA3B;EACE,OAAOpL,KAAK,CAACkR,IAAN,CAAW9F,OAAX,EACJhD,MADI,CACG,UAAA+I,EAAE;IAAA,OAAIA,EAAE,CAACC,QAAP;EAAA,CADL,EAEJ7I,GAFI,CAEA,UAAA4I,EAAE;IAAA,OAAIA,EAAE,CAACpR,KAAP;EAAA,CAFF,CAAP;AAGD;AAED;;AACA,SAAS6L,mBAATA,CACEyF,YADF,EAEEnG,OAFF,EAGEqD,SAHF;EAKE;EACA,IAAI,OAAO8C,YAAP,KAAwB,SAA5B,EAAuC;IACrC,OAAOC,OAAO,CAACpG,OAAD,CAAd;EACD;;EAGD,IAAIqG,oBAAoB,GAAG,EAA3B;EACA,IAAIC,cAAc,GAAG,KAArB;EACA,IAAI5I,KAAK,GAAG,CAAC,CAAb;EAEA,IAAI,CAAC5I,KAAK,CAACC,OAAN,CAAcoR,YAAd,CAAL,EAAkC;IAChC;IACA,IAAI,CAAC9C,SAAD,IAAcA,SAAS,IAAI,MAA3B,IAAqCA,SAAS,IAAI,OAAtD,EAA+D;MAC7D,OAAO+C,OAAO,CAACpG,OAAD,CAAd;IACD;EACF,CALD,MAKO;IACL;IACAqG,oBAAoB,GAAGF,YAAvB;IACAzI,KAAK,GAAGyI,YAAY,CAAC3C,OAAb,CAAqBH,SAArB,CAAR;IACAiD,cAAc,GAAG5I,KAAK,IAAI,CAA1B;EACD;;EAGD,IAAIsC,OAAO,IAAIqD,SAAX,IAAwB,CAACiD,cAA7B,EAA6C;IAC3C,OAAOD,oBAAoB,CAACE,MAArB,CAA4BlD,SAA5B,CAAP;EACD;;EAGD,IAAI,CAACiD,cAAL,EAAqB;IACnB,OAAOD,oBAAP;EACD;;EAGD,OAAOA,oBAAoB,CACxBzO,KADI,CACE,CADF,EACK8F,KADL,EAEJ6I,MAFI,CAEGF,oBAAoB,CAACzO,KAArB,CAA2B8F,KAAK,GAAG,CAAnC,CAFH,CAAP;AAGD;AAGD;AACA;AACA;;AACA,IAAM8I,yBAAyB,GAC7B,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAAC/P,QAAd,KAA2B,WAD3B,IAEA,OAAO+P,MAAM,CAAC/P,QAAP,CAAgByN,aAAvB,KAAyC,WAFzC,GAGIuC,eAHJ,GAIIzL,SALN;AAOA,SAASmD,gBAATA,CAA6DuI,EAA7D;EACE,IAAMC,GAAG,GAAQjM,MAAA,CAAagM,EAAb,CAAjB;;EAGAH,yBAAyB,CAAC;IACxBI,GAAG,CAAC1L,OAAJ,GAAcyL,EAAd;EACD,CAFwB,CAAzB;EAIA,OAAOlL,WAAA,CACL;IAAA,SAAAoL,IAAA,GAAAC,SAAA,CAAA9R,MAAA,EAAI+R,IAAJ,OAAAjS,KAAA,CAAA+R,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAID,IAAJ,CAAAC,IAAA,IAAAF,SAAA,CAAAE,IAAA;IAAA;IAAA,OAAoBJ,GAAG,CAAC1L,OAAJ,CAAY+L,KAAZ,CAAkB,KAAK,CAAvB,EAA0BF,IAA1B,CAApB;EAAA,CADK,EAEL,EAFK,CAAP;AAID;SC9mCeG,SACdC,gBAAA;EAEA,IAAM7S,MAAM,GAAGD,gBAAgB,EAA/B;MAEE0O,aAAA,GAKEzO,MAAA,CALFyO,aAAA;IACAN,YAAA,GAIEnO,MAAA,CAJFmO,YAAA;IACAG,eAAA,GAGEtO,MAAA,CAHFsO,eAAA;IACA9D,aAAA,GAEExK,MAAA,CAFFwK,aAAA;IACAE,eAAA,GACE1K,MAAA,CADF0K,eAAA;EAGF,IAAMiE,UAAU,GAAG9N,QAAQ,CAACgS,gBAAD,CAA3B;;EAGA,IAAM1M,KAAK,GAAyBwI,UAAU,GACzCkE,gBADyC,GAE1C;IAAEtK,IAAI,EAAEsK;EAAR,CAFJ;MAIcC,SAAA,GAAoC3M,KAAA,CAA1CoC,IAAA;IAA2BwK,UAAA,GAAe5M,KAAA,CAAzByB,QAAA;EAEzBjB,SAAA,CAAgB;IACd,IAAImM,SAAJ,EAAe;MACbtI,aAAa,CAACsI,SAAD,EAAY;QACvBlL,QAAQ,EAAEmL;MADa,CAAZ,CAAb;IAGD;IACD,OAAO;MACL,IAAID,SAAJ,EAAe;QACbpI,eAAe,CAACoI,SAAD,CAAf;MACD;IACF,CAJD;EAKD,CAXD,EAWG,CAACtI,aAAD,EAAgBE,eAAhB,EAAiCoI,SAAjC,EAA4CC,UAA5C,CAXH;EAaA,IAAA7S,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;IACX,CACEJ,MADF,GAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,4GAFO,CAAT,GAAAA,SAAS,OAAT;EAID;EAED,CACEyS,SADF,GAAA5S,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,2FAFO,CAAT,GAAAA,SAAS,OAAT;EAKA,IAAM2S,YAAY,GAAG5D,OAAA,CAAc;IAAA,OAAMd,eAAe,CAACwE,SAAD,CAArB;EAAA,CAAd,EAAgD,CACnExE,eADmE,EAEnEwE,SAFmE,CAAhD,CAArB;EAKA,OAAO,CAACrE,aAAa,CAACtI,KAAD,CAAd,EAAuBgI,YAAY,CAAC2E,SAAD,CAAnC,EAAgDE,YAAhD,CAAP;AACD;AAED,SAAgBC,MAAA3N,IAAA;MACdsC,QAAA,GAAAtC,IAAA,CAAAsC,QAAA;IACAW,IAAA,GAAAjD,IAAA,CAAAiD,IAAA;IACAmH,MAAA,GAAApK,IAAA,CAAAoK,MAAA;IACA/N,QAAA,GAAA2D,IAAA,CAAA3D,QAAA;IACIqN,EAAA,GAAA1J,IAAA,CAAJ2J,EAAA;IACAQ,SAAA,GAAAnK,IAAA,CAAAmK,SAAA;IACAyD,SAAA,GAAA5N,IAAA,CAAA4N,SAAA;IACG/M,KAAA,GAAAD,6BAAA,CAAAZ,IAAA;0BAOCvF,gBAAgB;IAJRC,MAGP,GAAAkG,6BAAA,CAAAiN,iBAAA;EAGL,IAAAjT,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;IACX;IACAuG,SAAA,CAAgB;MACd,CACE,CAAC+I,MADH,GAAAxP,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,kMAEgLkI,IAFhL,iEAE8OA,IAF9O,+CAAT,GAAAlI,SAAS,OAAT;MAKA,CACE,EAAE2O,EAAE,IAAIrN,QAAN,IAAkBhB,UAAU,CAACgB,QAAD,CAA9B,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,6HAFO,CAAT,GAAAA,SAAS,OAAT;MAKA,CACE,EAAEoP,SAAS,IAAI9N,QAAb,IAAyBhB,UAAU,CAACgB,QAAD,CAArC,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,2IAFO,CAAT,GAAAA,SAAS,OAAT;MAKA,CACE,EAAEqP,MAAM,IAAI/N,QAAV,IAAsB,CAACD,eAAe,CAACC,QAAD,CAAxC,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,wHAFO,CAAT,GAAAA,SAAS,OAAT;IAKD,CArBD,EAqBG,EArBH;EAsBD;;MAGOmK,aAAA,GAAmCxK,MAAA,CAAnCwK,aAAA;IAAeE,eAAA,GAAoB1K,MAAA,CAApB0K,eAAA;EACvB/D,SAAA,CAAgB;IACd6D,aAAa,CAACjC,IAAD,EAAO;MAClBX,QAAQ,EAAEA;IADQ,CAAP,CAAb;IAGA,OAAO;MACL8C,eAAe,CAACnC,IAAD,CAAf;IACD,CAFD;EAGD,CAPD,EAOG,CAACiC,aAAD,EAAgBE,eAAhB,EAAiCnC,IAAjC,EAAuCX,QAAvC,CAPH;EAQA,IAAM3C,KAAK,GAAGjF,MAAM,CAACyO,aAAP,CAAAjK,QAAA;IAAuB+D,IAAI,EAAJA;EAAvB,GAAgCpC,KAAhC,EAAd;EACA,IAAMiN,IAAI,GAAGpT,MAAM,CAACmO,YAAP,CAAoB5F,IAApB,CAAb;EACA,IAAM8K,SAAS,GAAG;IAAEpO,KAAK,EAALA,KAAF;IAASqO,IAAI,EAAEtT;EAAf,CAAlB;EAEA,IAAI0P,MAAJ,EAAY;IACV,OAAOA,MAAM,CAAAlL,QAAA,KAAM6O,SAAN;MAAiBD,IAAI,EAAJA;IAAjB,GAAb;EACD;EAED,IAAIzS,UAAU,CAACgB,QAAD,CAAd,EAA0B;IACxB,OAAOA,QAAQ,CAAA6C,QAAA,KAAM6O,SAAN;MAAiBD,IAAI,EAAJA;IAAjB,GAAf;EACD;EAED,IAAI3D,SAAJ,EAAe;IACb;IACA,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;MAAA,IACzBE,QADyB,GACHxJ,KADG,CACzBwJ,QADyB;QACZ1J,IADY,GAAAC,6BAAA,CACHC,KADG;MAEjC,OAAO0J,aAAA,CACLJ,SADK,EAAAjL,QAAA;QAEH8N,GAAG,EAAE3C;MAFF,GAEe1K,KAFf,EAEyBgB,IAFzB;QAE+BiN,SAAS,EAATA;MAF/B,IAGLvR,QAHK,CAAP;IAKD,CATY;;IAWb,OAAOkO,aAAA,CACLJ,SADK,EAAAjL,QAAA;MAEHS,KAAK,EAALA,KAFG;MAEIqO,IAAI,EAAEtT;IAFV,GAEqBmG,KAFrB;MAE4B+M,SAAS,EAATA;IAF5B,IAGLvR,QAHK,CAAP;EAKD;;EAGD,IAAM4R,SAAS,GAAGvE,EAAE,IAAI,OAAxB;EAEA,IAAI,OAAOuE,SAAP,KAAqB,QAAzB,EAAmC;IAAA,IACzBC,SADyB,GACHrN,KADG,CACzBwJ,QADyB;MACZ8D,KADY,GAAAvN,6BAAA,CACHC,KADG;IAEjC,OAAO0J,aAAA,CACL0D,SADK,EAAA/O,QAAA;MAEH8N,GAAG,EAAEkB;IAFF,GAEevO,KAFf,EAEyBwO,KAFzB;MAE+BP,SAAS,EAATA;IAF/B,IAGLvR,QAHK,CAAP;EAKD;EAED,OAAOkO,aAAA,CAAoB0D,SAApB,EAAA/O,QAAA,KAAoCS,KAApC,EAA8CkB,KAA9C;IAAqD+M,SAAS,EAATA;EAArD,IAAkEvR,QAAlE,CAAP;AACD;IC1NY+R,IAAI,gBAAGC,UAAA,CAClB,UAACxN,KAAD,EAAyBmM,GAAzB;EACE;EACA;MACQlL,MAAA,GAAoBjB,KAAA,CAApBiB,MAAA;IAAWnB,IAAA,GAAAC,6BAAA,CAASC,KAAA;EAC5B,IAAMyN,OAAO,GAAGxM,MAAH,WAAGA,MAAH,GAAa,GAA1B;0BACsCrH,gBAAgB;IAA9CmO,WAAA,GAAAiF,iBAAA,CAAAjF,WAAA;IAAaR,YAAA,GAAAyF,iBAAA,CAAAzF,YAAA;EACrB,OACEmC,aAAA,SAAArL,QAAA;IACEwB,QAAQ,EAAE0H,YADZ;IAEE4E,GAAG,EAAEA,GAFP;IAGEpI,OAAO,EAAEgE,WAHX;IAIE9G,MAAM,EAAEwM;EAJV,GAKM3N,IALN,EADF;AASD,CAhBiB,CAAb;AAmBPyN,IAAI,CAAChU,WAAL,GAAmB,MAAnB;;AC4DA;;;;AAGA,SAAgBmU,WAAAvO,IAAA;mCAKdwO,gBAAA;IAAAA,gBAAA,GAAAC,qBAAA,cAAmB,UAACC,YAAD;MACjB,IAAI/P,GAAG,GAAW,EAAlB;MACA,KAAK,IAAID,CAAT,IAAcgQ,YAAd,EAA4B;QAC1B,IACEA,YAAY,CAAChD,cAAb,CAA4BhN,CAA5B,KACA,OAAOgQ,YAAY,CAAChQ,CAAD,CAAnB,KAA2B,UAF7B,EAGE;UACA;UACCC,GAAW,CAACD,CAAD,CAAX,GAAiBgQ,YAAY,CAAChQ,CAAD,CAA7B;QACF;MACF;MACD,OAAOC,GAAP;IACD,IAAA8P,qBAAA;IACEE,MAAA,GAAA/N,6BAAA,CAAAZ,IAAA;EAKH,OAAO,SAAS4O,YAATA,CACLC,WADK;IAGL,IAAMC,oBAAoB,GACxBD,WAAS,CAACzU,WAAV,IACAyU,WAAS,CAAC5L,IADV,IAEC4L,WAAS,CAACE,WAAV,IAAyBF,WAAS,CAACE,WAAV,CAAsB9L,IAFhD,IAGA,WAJF;IAKA;;;;;QAIM+L,CAAA,0BAAAC,gBAAA;;;;;;;;cAGJ3M,QAAA,GAAW,UAACnD,MAAD;UACT,OAAOwP,MAAM,CAACrM,QAAP,CAAiBnD,MAAjB,EAAyB+P,KAAA,CAAKrO,KAA9B,CAAP;QACD;cAED8B,gBAAA,GAAmB;UACjB,OAAOtH,UAAU,CAACsT,MAAM,CAAChM,gBAAR,CAAV,GACHgM,MAAM,CAAChM,gBAAP,CAAyBuM,KAAA,CAAKrO,KAA9B,CADG,GAEH8N,MAAM,CAAChM,gBAFX;QAGD;cAEDyF,YAAA,GAAe,UAACjJ,MAAD,EAAiBgQ,OAAjB;UACb,OAAOR,MAAM,CAACvG,YAAP,CAAoBjJ,MAApB,EAAAD,QAAA,KACFiQ,OADE;YAELtO,KAAK,EAAEqO,KAAA,CAAKrO;UAFP,GAAP;QAID;cAKDuO,mBAAA,GAAsB,UAACC,WAAD;UACpB,OAAO9E,aAAA,CAACsE,WAAD,EAAA3P,QAAA,KAAegQ,KAAA,CAAKrO,KAApB,EAA+BwO,WAA/B,EAAP;QACD;;;;aAEDjF,MAAA,YAAAA,OAAA;0BACiC,KAAKvJ,KAAA;UAA5BA,KAAa,GAAAD,6BAAA,CAAA0O,WAAA;QACrB,OACE/E,aAAA,CAACN,MAAD,EAAA/K,QAAA,KACM2B,KADN,EAEM8N,MAFN;UAGErM,QAAQ,EAAEqM,MAAM,CAACrM,QAAP,IAAmB,KAAKA,QAHpC;UAIEK,gBAAgB,EAAEgM,MAAM,CAAChM,gBAAP,IAA2B,KAAKA,gBAJpD;UAKE7B,aAAa,EAAE0N,gBAAgB,CAAC,KAAK3N,KAAN,CALjC;UAMEK,aAAa,EACXyN,MAAM,CAACY,gBAAP,IAA2BZ,MAAM,CAACY,gBAAP,CAAwB,KAAK1O,KAA7B,CAP/B;UASEG,aAAa,EACX2N,MAAM,CAACa,gBAAP,IAA2Bb,MAAM,CAACa,gBAAP,CAAwB,KAAK3O,KAA7B,CAV/B;UAYEI,cAAc,EACZ0N,MAAM,CAACc,iBAAP,IAA4Bd,MAAM,CAACc,iBAAP,CAAyB,KAAK5O,KAA9B,CAbhC;UAeEH,QAAQ,EAAE,KAAK0H,YAfjB;UAgBE/L,QAAQ,EAAE,KAAK+S;QAhBjB,GADF;MAoBD;;MAjDaM,SAAA;IAAVV,CAAA,CACG5U,WAAA,mBAA4B0U,oBAAA;IAmDrC,OAAOa,oBAAoB,CACzBX,CADyB,EAEzBH,WAFyB;IAAA,CAA3B;EAID,CApED;AAqED;;ACrLD;;;;;AAIA,SAAgBe,QACdC,IAAA;EAEA,IAAMb,CAAC,GAAyB,SAA1BA,CAA0BA,CAAAnO,KAAK;IAAA,OACnC0J,aAAA,CAAChQ,cAAD,QACG,UAAAG,MAAM;MACL,CACE,CAAC,CAACA,MADJ,GAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,iNAEgM8U,IAAI,CAAC5M,IAFrM,CAAT,GAAAlI,SAAS,OAAT;MAIA,OAAOwP,aAAA,CAACsF,IAAD,EAAA3Q,QAAA,KAAU2B,KAAV;QAAiBnG,MAAM,EAAEA;MAAzB,GAAP;IACD,CAPH,CADmC;EAAA,CAArC;EAYA,IAAMoU,oBAAoB,GACxBe,IAAI,CAACzV,WAAL,IACAyV,IAAI,CAAC5M,IADL,IAEC4M,IAAI,CAACd,WAAL,IAAoBc,IAAI,CAACd,WAAL,CAAiB9L,IAFtC,IAGA,WAJF;EAOA;;EACC+L,CAEC,CAACc,gBAFF,GAEqBD,IAFrB;EAIDb,CAAC,CAAC5U,WAAF,sBAAiC0U,oBAAjC;EAEA,OAAOa,oBAAoB,CACzBX,CADyB,EAEzBa,IAFyB;EAAA,CAA3B;AAMD;;ACmBD;;;;AAGA,IAAaE,IAAI,GAAG,SAAPA,IAAOA,CAAKC,KAAL,EAAiB5D,IAAjB,EAA+B6D,EAA/B;EAClB,IAAMC,IAAI,GAAGC,aAAa,CAACH,KAAD,CAA1B;EACA,IAAM/U,KAAK,GAAGiV,IAAI,CAAC9D,IAAD,CAAlB;EACA8D,IAAI,CAACE,MAAL,CAAYhE,IAAZ,EAAkB,CAAlB;EACA8D,IAAI,CAACE,MAAL,CAAYH,EAAZ,EAAgB,CAAhB,EAAmBhV,KAAnB;EACA,OAAOiV,IAAP;AACD,CANM;AAQP,IAAaG,IAAI,GAAG,SAAPA,IAAOA,CAClBC,SADkB,EAElBC,MAFkB,EAGlBC,MAHkB;EAKlB,IAAMN,IAAI,GAAGC,aAAa,CAACG,SAAD,CAA1B;EACA,IAAMG,CAAC,GAAGP,IAAI,CAACK,MAAD,CAAd;EACAL,IAAI,CAACK,MAAD,CAAJ,GAAeL,IAAI,CAACM,MAAD,CAAnB;EACAN,IAAI,CAACM,MAAD,CAAJ,GAAeC,CAAf;EACA,OAAOP,IAAP;AACD,CAVM;AAYP,IAAaQ,MAAM,GAAG,SAATA,MAASA,CACpBJ,SADoB,EAEpBxM,KAFoB,EAGpB7I,KAHoB;EAKpB,IAAMiV,IAAI,GAAGC,aAAa,CAACG,SAAD,CAA1B;EACAJ,IAAI,CAACE,MAAL,CAAYtM,KAAZ,EAAmB,CAAnB,EAAsB7I,KAAtB;EACA,OAAOiV,IAAP;AACD,CARM;AAUP,IAAaS,OAAO,GAAG,SAAVA,OAAUA,CACrBL,SADqB,EAErBxM,KAFqB,EAGrB7I,KAHqB;EAKrB,IAAMiV,IAAI,GAAGC,aAAa,CAACG,SAAD,CAA1B;EACAJ,IAAI,CAACpM,KAAD,CAAJ,GAAc7I,KAAd;EACA,OAAOiV,IAAP;AACD,CARM;AAUP,IAAMC,aAAa,GAAG,SAAhBA,aAAgBA,CAACG,SAAD;EACpB,IAAI,CAACA,SAAL,EAAgB;IACd,OAAO,EAAP;EACD,CAFD,MAEO,IAAIpV,KAAK,CAACC,OAAN,CAAcmV,SAAd,CAAJ,EAA8B;IACnC,UAAA3D,MAAA,CAAW2D,SAAX;EACD,CAFM,MAEA;IACL,IAAMM,QAAQ,GAAG9U,MAAM,CAAC2C,IAAP,CAAY6R,SAAZ,EACd7M,GADc,CACV,UAAAtG,GAAG;MAAA,OAAI0T,QAAQ,CAAC1T,GAAD,CAAZ;IAAA,CADO,EAEdyG,MAFc,CAEP,UAACkN,GAAD,EAAMzE,EAAN;MAAA,OAAcA,EAAE,GAAGyE,GAAL,GAAWzE,EAAX,GAAgByE,GAA9B;IAAA,CAFO,EAE6B,CAF7B,CAAjB;IAGA,OAAO5V,KAAK,CAACkR,IAAN,CAAAlN,QAAA,KAAgBoR,SAAhB;MAA2BlV,MAAM,EAAEwV,QAAQ,GAAG;IAA9C,GAAP;EACD;AACF,CAXD;AAaA,IAAMG,uBAAuB,GAAG,SAA1BA,uBAA0BA,CAC9BC,UAD8B,EAE9BC,eAF8B;EAI9B,IAAMlE,EAAE,GAAG,OAAOiE,UAAP,KAAsB,UAAtB,GAAmCA,UAAnC,GAAgDC,eAA3D;EAEA,OAAO,UAACxF,IAAD;IACL,IAAIvQ,KAAK,CAACC,OAAN,CAAcsQ,IAAd,KAAuBlQ,QAAQ,CAACkQ,IAAD,CAAnC,EAA2C;MACzC,IAAM/N,KAAK,GAAGyS,aAAa,CAAC1E,IAAD,CAA3B;MACA,OAAOsB,EAAE,CAACrP,KAAD,CAAT;IACD;IAGD;;IACA,OAAO+N,IAAP;EACD,CATD;AAUD,CAhBD;IAkBMyF,eAAA,0BAAAjC,gBAAA;;EAQJ,SAAAiC,gBAAYrQ,KAAZ;;IACEqO,KAAA,GAAAD,gBAAA,CAAAhT,IAAA,OAAM4E,KAAN;IAEA;;UAoBFsQ,gBAAA,GAAmB,UACjBpE,EADiB,EAEjBqE,YAFiB,EAGjBC,WAHiB;wBASbnC,KAAA,CAAKrO,KAAA;QAHPoC,IAAA,GAAAqM,WAAA,CAAArM,IAAA;QAEUuE,cAAA,GAAA8H,WAAA,CAAV5U,MAAA,CAAU8M,cAAA;MAGZA,cAAc,CAAC,UAAC8J,SAAD;QACb,IAAIC,YAAY,GAAGR,uBAAuB,CAACM,WAAD,EAActE,EAAd,CAA1C;QACA,IAAIyE,aAAa,GAAGT,uBAAuB,CAACK,YAAD,EAAerE,EAAf,CAA3C;QAGA;;QACA,IAAI5N,MAAM,GAAG3B,KAAK,CAChB8T,SAAS,CAACnS,MADM,EAEhB8D,IAFgB,EAGhB8J,EAAE,CAAC7P,KAAK,CAACoU,SAAS,CAACnS,MAAX,EAAmB8D,IAAnB,CAAN,CAHc,CAAlB;QAMA,IAAIwO,UAAU,GAAGJ,WAAW,GACxBE,YAAY,CAACrU,KAAK,CAACoU,SAAS,CAAC/R,MAAX,EAAmB0D,IAAnB,CAAN,CADY,GAExB9I,SAFJ;QAGA,IAAIuX,YAAY,GAAGN,YAAY,GAC3BI,aAAa,CAACtU,KAAK,CAACoU,SAAS,CAACjS,OAAX,EAAoB4D,IAApB,CAAN,CADc,GAE3B9I,SAFJ;QAIA,IAAIa,YAAY,CAACyW,UAAD,CAAhB,EAA8B;UAC5BA,UAAU,GAAGtX,SAAb;QACD;QACD,IAAIa,YAAY,CAAC0W,YAAD,CAAhB,EAAgC;UAC9BA,YAAY,GAAGvX,SAAf;QACD;QAED,OAAA+E,QAAA,KACKoS,SADL;UAEEnS,MAAM,EAANA,MAFF;UAGEI,MAAM,EAAE8R,WAAW,GACf7T,KAAK,CAAC8T,SAAS,CAAC/R,MAAX,EAAmB0D,IAAnB,EAAyBwO,UAAzB,CADU,GAEfH,SAAS,CAAC/R,MALhB;UAMEF,OAAO,EAAE+R,YAAY,GACjB5T,KAAK,CAAC8T,SAAS,CAACjS,OAAX,EAAoB4D,IAApB,EAA0ByO,YAA1B,CADY,GAEjBJ,SAAS,CAACjS;QARhB;MAUD,CApCa,CAAd;IAqCD;UAED8M,IAAA,GAAO,UAAClR,KAAD;MAAA,OACLiU,KAAA,CAAKiC,gBAAL,CACE,UAACb,SAAD;QAAA,UAAA3D,MAAA,CACKwD,aAAa,CAACG,SAAD,CADlB,GAEE3O,SAAS,CAAC1G,KAAD,CAFX;MAAA,CADF,EAKE,KALF,EAME,KANF,CADK;IAAA;UAUP0W,UAAA,GAAa,UAAC1W,KAAD;MAAA,OAAgB;QAAA,OAAMiU,KAAA,CAAK/C,IAAL,CAAUlR,KAAV,CAAN;MAAA,CAAhB;IAAA;UAEboV,IAAA,GAAO,UAACE,MAAD,EAAiBC,MAAjB;MAAA,OACLtB,KAAA,CAAKiC,gBAAL,CACE,UAACnB,KAAD;QAAA,OAAkBK,IAAI,CAACL,KAAD,EAAQO,MAAR,EAAgBC,MAAhB,CAAtB;MAAA,CADF,EAEE,IAFF,EAGE,IAHF,CADK;IAAA;UAOPoB,UAAA,GAAa,UAACrB,MAAD,EAAiBC,MAAjB;MAAA,OAAoC;QAAA,OAC/CtB,KAAA,CAAKmB,IAAL,CAAUE,MAAV,EAAkBC,MAAlB,CAD+C;MAAA,CAApC;IAAA;UAGbT,IAAA,GAAO,UAAC3D,IAAD,EAAe6D,EAAf;MAAA,OACLf,KAAA,CAAKiC,gBAAL,CAAsB,UAACnB,KAAD;QAAA,OAAkBD,IAAI,CAACC,KAAD,EAAQ5D,IAAR,EAAc6D,EAAd,CAAtB;MAAA,CAAtB,EAA+D,IAA/D,EAAqE,IAArE,CADK;IAAA;UAGP4B,UAAA,GAAa,UAACzF,IAAD,EAAe6D,EAAf;MAAA,OAA8B;QAAA,OAAMf,KAAA,CAAKa,IAAL,CAAU3D,IAAV,EAAgB6D,EAAhB,CAAN;MAAA,CAA9B;IAAA;UAEbS,MAAA,GAAS,UAAC5M,KAAD,EAAgB7I,KAAhB;MAAA,OACPiU,KAAA,CAAKiC,gBAAL,CACE,UAACnB,KAAD;QAAA,OAAkBU,MAAM,CAACV,KAAD,EAAQlM,KAAR,EAAe7I,KAAf,CAAxB;MAAA,CADF,EAEE,UAAC+U,KAAD;QAAA,OAAkBU,MAAM,CAACV,KAAD,EAAQlM,KAAR,EAAe,IAAf,CAAxB;MAAA,CAFF,EAGE,UAACkM,KAAD;QAAA,OAAkBU,MAAM,CAACV,KAAD,EAAQlM,KAAR,EAAe,IAAf,CAAxB;MAAA,CAHF,CADO;IAAA;UAOTgO,YAAA,GAAe,UAAChO,KAAD,EAAgB7I,KAAhB;MAAA,OAA+B;QAAA,OAAMiU,KAAA,CAAKwB,MAAL,CAAY5M,KAAZ,EAAmB7I,KAAnB,CAAN;MAAA,CAA/B;IAAA;UAEf0V,OAAA,GAAU,UAAC7M,KAAD,EAAgB7I,KAAhB;MAAA,OACRiU,KAAA,CAAKiC,gBAAL,CACE,UAACnB,KAAD;QAAA,OAAkBW,OAAO,CAACX,KAAD,EAAQlM,KAAR,EAAe7I,KAAf,CAAzB;MAAA,CADF,EAEE,KAFF,EAGE,KAHF,CADQ;IAAA;UAOV8W,aAAA,GAAgB,UAACjO,KAAD,EAAgB7I,KAAhB;MAAA,OAA+B;QAAA,OAC7CiU,KAAA,CAAKyB,OAAL,CAAa7M,KAAb,EAAoB7I,KAApB,CAD6C;MAAA,CAA/B;IAAA;UAGhB+W,OAAA,GAAU,UAAC/W,KAAD;MACR,IAAIG,MAAM,GAAG,CAAC,CAAd;MACA8T,KAAA,CAAKiC,gBAAL,CACE,UAACnB,KAAD;QACE,IAAMiC,GAAG,GAAGjC,KAAK,IAAI/U,KAAJ,EAAA0R,MAAA,CAAcqD,KAAd,IAAuB,CAAC/U,KAAD,CAAxC;QAEAG,MAAM,GAAG6W,GAAG,CAAC7W,MAAb;QAEA,OAAO6W,GAAP;MACD,CAPH,EAQE,UAACjC,KAAD;QACE,OAAOA,KAAK,IAAI,IAAJ,EAAArD,MAAA,CAAaqD,KAAb,IAAsB,CAAC,IAAD,CAAlC;MACD,CAVH,EAWE,UAACA,KAAD;QACE,OAAOA,KAAK,IAAI,IAAJ,EAAArD,MAAA,CAAaqD,KAAb,IAAsB,CAAC,IAAD,CAAlC;MACD,CAbH;MAgBA,OAAO5U,MAAP;IACD;UAED8W,aAAA,GAAgB,UAACjX,KAAD;MAAA,OAAgB;QAAA,OAAMiU,KAAA,CAAK8C,OAAL,CAAa/W,KAAb,CAAN;MAAA,CAAhB;IAAA;UA6BhBkX,YAAA,GAAe,UAACrO,KAAD;MAAA,OAAmB;QAAA,OAAMoL,KAAA,CAAKkD,MAAL,CAAiBtO,KAAjB,CAAN;MAAA,CAAnB;IAAA;UAqBfuO,SAAA,GAAY;MAAA,OAAM;QAAA,OAAMnD,KAAA,CAAKoD,GAAL,EAAN;MAAA,CAAN;IAAA;IA1LVpD,KAAA,CAAKkD,MAAL,GAAclD,KAAA,CAAKkD,MAAL,CAAYG,IAAZ,CAAAC,sBAAA,CAAAtD,KAAA,EAAd;IACAA,KAAA,CAAKoD,GAAL,GAAWpD,KAAA,CAAKoD,GAAL,CAASC,IAAT,CAAAC,sBAAA,CAAAtD,KAAA,EAAX;;EACD;;SAEDuD,kBAAA,YAAAA,mBACEC,SADF;IAGE,IACE,KAAK7R,KAAL,CAAWZ,gBAAX,IACA,KAAKY,KAAL,CAAWnG,MAAX,CAAkBuF,gBADlB,IAEA,CAACX,OAAO,CACNpC,KAAK,CAACwV,SAAS,CAAChY,MAAV,CAAiByE,MAAlB,EAA0BuT,SAAS,CAACzP,IAApC,CADC,EAEN/F,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkByE,MAAnB,EAA2B,KAAK0B,KAAL,CAAWoC,IAAtC,CAFC,CAHV,EAOE;MACA,KAAKpC,KAAL,CAAWnG,MAAX,CAAkBiO,YAAlB,CAA+B,KAAK9H,KAAL,CAAWnG,MAAX,CAAkByE,MAAjD;IACD;EACF;SAyHDiT,MAAA,YAAAA,OAAUtO,KAAV;IACE;IACA,IAAIoE,MAAJ;IACA,KAAKiJ,gBAAL;IAAA;IAEE,UAACnB,KAAD;MACE,IAAME,IAAI,GAAGF,KAAK,GAAGG,aAAa,CAACH,KAAD,CAAhB,GAA0B,EAA5C;MACA,IAAI,CAAC9H,MAAL,EAAa;QACXA,MAAM,GAAGgI,IAAI,CAACpM,KAAD,CAAb;MACD;MACD,IAAIzI,UAAU,CAAC6U,IAAI,CAACE,MAAN,CAAd,EAA6B;QAC3BF,IAAI,CAACE,MAAL,CAAYtM,KAAZ,EAAmB,CAAnB;MACD;;MAED,OAAOzI,UAAU,CAAC6U,IAAI,CAACyC,KAAN,CAAV,GACHzC,IAAI,CAACyC,KAAL,CAAW,UAAAC,CAAC;QAAA,OAAIA,CAAC,KAAKzY,SAAV;MAAA,CAAZ,IACE,EADF,GAEE+V,IAHC,GAIHA,IAJJ;IAKD,CAhBH,EAiBE,IAjBF,EAkBE,IAlBF;IAqBA,OAAOhI,MAAP;EACD;SAIDoK,GAAA,YAAAA,IAAA;IACE;IACA,IAAIpK,MAAJ;IACA,KAAKiJ,gBAAL;IAAA;IAEE,UAACnB,KAAD;MACE,IAAM6C,GAAG,GAAG7C,KAAK,CAAChS,KAAN,EAAZ;MACA,IAAI,CAACkK,MAAL,EAAa;QACXA,MAAM,GAAG2K,GAAG,IAAIA,GAAG,CAACP,GAAX,IAAkBO,GAAG,CAACP,GAAJ,EAA3B;MACD;MACD,OAAOO,GAAP;IACD,CARH,EASE,IATF,EAUE,IAVF;IAaA,OAAO3K,MAAP;EACD;SAIDkC,MAAA,YAAAA,OAAA;IACE,IAAM0I,YAAY,GAAiB;MACjC3G,IAAI,EAAE,KAAKA,IADsB;MAEjCmG,GAAG,EAAE,KAAKA,GAFuB;MAGjCjC,IAAI,EAAE,KAAKA,IAHsB;MAIjCN,IAAI,EAAE,KAAKA,IAJsB;MAKjCW,MAAM,EAAE,KAAKA,MALoB;MAMjCC,OAAO,EAAE,KAAKA,OANmB;MAOjCqB,OAAO,EAAE,KAAKA,OAPmB;MAQjCI,MAAM,EAAE,KAAKA,MARoB;MASjCT,UAAU,EAAE,KAAKA,UATgB;MAUjCU,SAAS,EAAE,KAAKA,SAViB;MAWjCT,UAAU,EAAE,KAAKA,UAXgB;MAYjCC,UAAU,EAAE,KAAKA,UAZgB;MAajCC,YAAY,EAAE,KAAKA,YAbc;MAcjCC,aAAa,EAAE,KAAKA,aAda;MAejCG,aAAa,EAAE,KAAKA,aAfa;MAgBjCC,YAAY,EAAE,KAAKA;IAhBc,CAAnC;uBA6BI,KAAKtR,KAAA;MATPsJ,SAAA,GAAA4I,YAAA,CAAA5I,SAAA;MACAC,MAAA,GAAA2I,YAAA,CAAA3I,MAAA;MACA/N,QAAA,GAAA0W,YAAA,CAAA1W,QAAA;MACA4G,IAAA,GAAA8P,YAAA,CAAA9P,IAAA;yCACAvI,MAAA;MACYsY,YAEP,GAAApS,6BAAA,CAAAqS,mBAAA;IAIP,IAAMpS,KAAK,GAAA3B,QAAA,KACN4T,YADM;MAET9E,IAAI,EAAEgF,YAFG;MAGT/P,IAAI,EAAJA;IAHS,EAAX;IAMA,OAAOkH,SAAS,GACZI,aAAA,CAAoBJ,SAApB,EAAsCtJ,KAAtC,CADY,GAEZuJ,MAAM,GACLA,MAAc,CAACvJ,KAAD,CADT,GAENxE,QAAQ;IAAA,EACR,OAAOA,QAAP,KAAoB,UAApB,GACGA,QAAgB,CAACwE,KAAD,CADnB,GAEE,CAACzE,eAAe,CAACC,QAAD,CAAhB,GACAC,QAAA,CAAekO,IAAf,CAAoBnO,QAApB,CADA,GAEA,IALM,GAMR,IAVJ;EAWD;;EAzPwCqT,SAAA;AAArCwB,eAAA,CAIGgC,YAAA,GAAe;EACpBjT,gBAAgB,EAAE;AADE;AAwPxB,IAAakT,UAAU,gBAAGvD,OAAO,CAAwBsB,eAAxB,CAA1B;ICzXDkC,gBAAA,0BAAAnE,gBAAA;;;;;;SAGJoE,qBAAA,YAAAA,sBACExS,KADF;IAGE,IACE3D,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkB6E,MAAnB,EAA2B,KAAKsB,KAAL,CAAWoC,IAAtC,CAAL,KACE/F,KAAK,CAAC2D,KAAK,CAACnG,MAAN,CAAa6E,MAAd,EAAsB,KAAKsB,KAAL,CAAWoC,IAAjC,CADP,IAEA/F,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkB2E,OAAnB,EAA4B,KAAKwB,KAAL,CAAWoC,IAAvC,CAAL,KACE/F,KAAK,CAAC2D,KAAK,CAACnG,MAAN,CAAa2E,OAAd,EAAuB,KAAKwB,KAAL,CAAWoC,IAAlC,CAHP,IAIAnH,MAAM,CAAC2C,IAAP,CAAY,KAAKoC,KAAjB,EAAwBzF,MAAxB,KAAmCU,MAAM,CAAC2C,IAAP,CAAYoC,KAAZ,EAAmBzF,MALxD,EAME;MACA,OAAO,IAAP;IACD,CARD,MAQO;MACL,OAAO,KAAP;IACD;EACF;SAEDgP,MAAA,YAAAA,OAAA;sBAC+D,KAAKvJ,KAAA;MAA5DsJ,SAAA,GAAAmF,WAAA,CAAAnF,SAAA;MAAWzP,MAAA,GAAA4U,WAAA,CAAA5U,MAAA;MAAQ0P,MAAA,GAAAkF,WAAA,CAAAlF,MAAA;MAAQ/N,QAAA,GAAAiT,WAAA,CAAAjT,QAAA;MAAU4G,IAAA,GAAAqM,WAAA,CAAArM,IAAA;MAAStC,IAAA,GAAAC,6BAAA,CAAA0O,WAAA;IAEpD,IAAMgE,KAAK,GAAGpW,KAAK,CAACxC,MAAM,CAAC2E,OAAR,EAAiB4D,IAAjB,CAAnB;IACA,IAAMgC,KAAK,GAAG/H,KAAK,CAACxC,MAAM,CAAC6E,MAAR,EAAgB0D,IAAhB,CAAnB;IAEA,OAAO,CAAC,CAACqQ,KAAF,IAAW,CAAC,CAACrO,KAAb,GACHmF,MAAM,GACJ/O,UAAU,CAAC+O,MAAD,CAAV,GACEA,MAAM,CAACnF,KAAD,CADR,GAEE,IAHE,GAIJ5I,QAAQ,GACRhB,UAAU,CAACgB,QAAD,CAAV,GACEA,QAAQ,CAAC4I,KAAD,CADV,GAEE,IAHM,GAIRkF,SAAS,GACTI,aAAA,CAAoBJ,SAApB,EAA+BxJ,IAA/B,EAA4CsE,KAA5C,CADS,GAETA,KAXC,GAYH,IAZJ;EAaD;;EAtC4ByK,SAAA;AAyC/B,IAAa6D,YAAY,gBAAG3D,OAAO,CAGjCwD,gBAHiC,CAA5B;;ACjBP;;;;;IAIMI,cAAA,0BAAAvE,gBAAA;;EAIJ,SAAAuE,eAAY3S,KAAZ;;IACEqO,KAAA,GAAAD,gBAAA,CAAAhT,IAAA,OAAM4E,KAAN;QACQuJ,MAAA,GAA8CvJ,KAAA,CAA9CuJ,MAAA;MAAQ/N,QAAA,GAAsCwE,KAAA,CAAtCxE,QAAA;MAAU8N,SAAA,GAA4BtJ,KAAA,CAA5BsJ,SAAA;MAAeT,EAAA,GAAa7I,KAAA,CAAjB8I,EAAA;MAAQ1G,IAAA,GAASpC,KAAA,CAAToC,IAAA;IAC7C,CACE,CAACmH,MADH,GAAAxP,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,oHAEmGkI,IAFnG,2CAAT,GAAAlI,SAAS,OAAT;IAIA,CACE,EAAEoP,SAAS,IAAIC,MAAf,CADF,GAAAxP,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,0IAFO,CAAT,GAAAA,SAAS,OAAT;IAKA,CACE,EAAE2O,EAAE,IAAIrN,QAAN,IAAkBhB,UAAU,CAACgB,QAAD,CAA9B,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,6IAFO,CAAT,GAAAA,SAAS,OAAT;IAKA,CACE,EAAEoP,SAAS,IAAI9N,QAAb,IAAyBhB,UAAU,CAACgB,QAAD,CAArC,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,2JAFO,CAAT,GAAAA,SAAS,OAAT;IAKA,CACE,EAAEqP,MAAM,IAAI/N,QAAV,IAAsB,CAACD,eAAe,CAACC,QAAD,CAAxC,CADF,GAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,SAAS,QAEP,wIAFO,CAAT,GAAAA,SAAS,OAAT;;EAID;;SAEDsY,qBAAA,YAAAA,sBAAsBxS,KAAtB;IACE,IAAI,KAAKA,KAAL,CAAW4S,YAAf,EAA6B;MAC3B,OAAO,KAAK5S,KAAL,CAAW4S,YAAX,CAAwB5S,KAAxB,EAA+B,KAAKA,KAApC,CAAP;IACD,CAFD,MAEO,IACLA,KAAK,CAACoC,IAAN,KAAe,KAAKpC,KAAL,CAAWoC,IAA1B,IACA/F,KAAK,CAAC2D,KAAK,CAACnG,MAAN,CAAayE,MAAd,EAAsB,KAAK0B,KAAL,CAAWoC,IAAjC,CAAL,KACE/F,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkByE,MAAnB,EAA2B,KAAK0B,KAAL,CAAWoC,IAAtC,CAFP,IAGA/F,KAAK,CAAC2D,KAAK,CAACnG,MAAN,CAAa6E,MAAd,EAAsB,KAAKsB,KAAL,CAAWoC,IAAjC,CAAL,KACE/F,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkB6E,MAAnB,EAA2B,KAAKsB,KAAL,CAAWoC,IAAtC,CAJP,IAKA/F,KAAK,CAAC2D,KAAK,CAACnG,MAAN,CAAa2E,OAAd,EAAuB,KAAKwB,KAAL,CAAWoC,IAAlC,CAAL,KACE/F,KAAK,CAAC,KAAK2D,KAAL,CAAWnG,MAAX,CAAkB2E,OAAnB,EAA4B,KAAKwB,KAAL,CAAWoC,IAAvC,CANP,IAOAnH,MAAM,CAAC2C,IAAP,CAAY,KAAKoC,KAAjB,EAAwBzF,MAAxB,KAAmCU,MAAM,CAAC2C,IAAP,CAAYoC,KAAZ,EAAmBzF,MAPtD,IAQAyF,KAAK,CAACnG,MAAN,CAAa+E,YAAb,KAA8B,KAAKoB,KAAL,CAAWnG,MAAX,CAAkB+E,YAT3C,EAUL;MACA,OAAO,IAAP;IACD,CAZM,MAYA;MACL,OAAO,KAAP;IACD;EACF;SAEDiU,iBAAA,YAAAA,kBAAA;IACE;IACA;IACA,KAAK7S,KAAL,CAAWnG,MAAX,CAAkBwK,aAAlB,CAAgC,KAAKrE,KAAL,CAAWoC,IAA3C,EAAiD;MAC/CX,QAAQ,EAAE,KAAKzB,KAAL,CAAWyB;IAD0B,CAAjD;EAGD;SAEDmQ,kBAAA,YAAAA,mBAAmBC,SAAnB;IACE,IAAI,KAAK7R,KAAL,CAAWoC,IAAX,KAAoByP,SAAS,CAACzP,IAAlC,EAAwC;MACtC,KAAKpC,KAAL,CAAWnG,MAAX,CAAkB0K,eAAlB,CAAkCsN,SAAS,CAACzP,IAA5C;MACA,KAAKpC,KAAL,CAAWnG,MAAX,CAAkBwK,aAAlB,CAAgC,KAAKrE,KAAL,CAAWoC,IAA3C,EAAiD;QAC/CX,QAAQ,EAAE,KAAKzB,KAAL,CAAWyB;MAD0B,CAAjD;IAGD;IAED,IAAI,KAAKzB,KAAL,CAAWyB,QAAX,KAAwBoQ,SAAS,CAACpQ,QAAtC,EAAgD;MAC9C,KAAKzB,KAAL,CAAWnG,MAAX,CAAkBwK,aAAlB,CAAgC,KAAKrE,KAAL,CAAWoC,IAA3C,EAAiD;QAC/CX,QAAQ,EAAE,KAAKzB,KAAL,CAAWyB;MAD0B,CAAjD;IAGD;EACF;SAEDqR,oBAAA,YAAAA,qBAAA;IACE,KAAK9S,KAAL,CAAWnG,MAAX,CAAkB0K,eAAlB,CAAkC,KAAKvE,KAAL,CAAWoC,IAA7C;EACD;SAEDmH,MAAA,YAAAA,OAAA;sBAWM,KAAKvJ,KAAA;MATPoC,IACA,GAAAqM,WAAA,CAAArM,IAAA;MACAmH,MAAA,GAAAkF,WAAA,CAAAlF,MAAA;MACIV,EAAA,GAAA4F,WAAA,CAAJ3F,EAAA;MACAtN,QAAA,GAAAiT,WAAA,CAAAjT,QAAA;MACA8N,SAAA,GAAAmF,WAAA,CAAAnF,SAAA;MACAzP,MACA,GAAA4U,WAAA,CAAA5U,MAAA;MACGmG,KAAA,GAAAD,6BAAA,CAAA0O,WAAA;QAMA0D,YAAA,GAAApS,6BAAA,CACDlG,MAAA;IACJ,IAAMiF,KAAK,GAAGjF,MAAM,CAACyO,aAAP,CAAAjK,QAAA;MAAuB+D,IAAI,EAAJA;IAAvB,GAAgCpC,KAAhC,EAAd;IACA,IAAMiN,IAAI,GAAG;MACX7S,KAAK,EAAEiC,KAAK,CAACxC,MAAM,CAACyE,MAAR,EAAgB8D,IAAhB,CADD;MAEXgC,KAAK,EAAE/H,KAAK,CAACxC,MAAM,CAAC6E,MAAR,EAAgB0D,IAAhB,CAFD;MAGX5D,OAAO,EAAE,CAAC,CAACnC,KAAK,CAACxC,MAAM,CAAC2E,OAAR,EAAiB4D,IAAjB,CAHL;MAIX6F,YAAY,EAAE5L,KAAK,CAACxC,MAAM,CAACoG,aAAR,EAAuBmC,IAAvB,CAJR;MAKXhC,cAAc,EAAE,CAAC,CAAC/D,KAAK,CAACxC,MAAM,CAACuG,cAAR,EAAwBgC,IAAxB,CALZ;MAMX8F,YAAY,EAAE7L,KAAK,CAACxC,MAAM,CAACsG,aAAR,EAAuBiC,IAAvB;IANR,CAAb;IASA,IAAM2Q,GAAG,GAAG;MAAEjU,KAAK,EAALA,KAAF;MAASmO,IAAI,EAAJA,IAAT;MAAeE,IAAI,EAAEgF;IAArB,CAAZ;IAEA,IAAI5I,MAAJ,EAAY;MACV,OAAQA,MAAc,CAACwJ,GAAD,CAAtB;IACD;IAED,IAAIvY,UAAU,CAACgB,QAAD,CAAd,EAA0B;MACxB,OAAQA,QAA4D,CAACuX,GAAD,CAApE;IACD;IAED,IAAIzJ,SAAJ,EAAe;MACb;MACA,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;QAAA,IACzBE,QADyB,GACHxJ,KADG,CACzBwJ,QADyB;UACZ1J,IADY,GAAAC,6BAAA,CACHC,KADG;QAEjC,OAAO0J,aAAA,CACLJ,SADK,EAAAjL,QAAA;UAEH8N,GAAG,EAAE3C;QAFF,GAEe1K,KAFf,EAE0BgB,IAF1B,GAGLtE,QAHK,CAAP;MAKD,CATY;;MAWb,OAAOkO,aAAA,CACLJ,SADK,EAAAjL,QAAA;QAEHS,KAAK,EAALA,KAFG;QAEIqO,IAAI,EAAEtT;MAFV,GAEqBmG,KAFrB,GAGLxE,QAHK,CAAP;IAKD;;IAGD,IAAM4R,SAAS,GAAGvE,EAAE,IAAI,OAAxB;IAEA,IAAI,OAAOuE,SAAP,KAAqB,QAAzB,EAAmC;MAAA,IACzBC,SADyB,GACHrN,KADG,CACzBwJ,QADyB;QACZ8D,KADY,GAAAvN,6BAAA,CACHC,KADG;MAEjC,OAAO0J,aAAA,CACL0D,SADK,EAAA/O,QAAA;QAEH8N,GAAG,EAAEkB;MAFF,GAEevO,KAFf,EAE0BwO,KAF1B,GAGL9R,QAHK,CAAP;IAKD;IAED,OAAOkO,aAAA,CACL0D,SADK,EAAA/O,QAAA,KAEAS,KAFA,EAEUkB,KAFV,GAGLxE,QAHK,CAAP;EAKD;;EAxJmDqT,SAAA;AA2JtD,IAAamE,SAAS,gBAAGjE,OAAO,CAAgC4D,cAAhC,CAAzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}