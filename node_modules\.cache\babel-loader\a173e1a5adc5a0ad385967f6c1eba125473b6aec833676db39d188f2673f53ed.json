{"ast": null, "code": "'use client';\n\nexport { default } from './Alert';\nexport { default as alertClasses } from './alertClasses';\nexport * from './alertClasses';", "map": {"version": 3, "names": ["default", "alertClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Alert/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Alert';\nexport { default as alertClasses } from './alertClasses';\nexport * from './alertClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}