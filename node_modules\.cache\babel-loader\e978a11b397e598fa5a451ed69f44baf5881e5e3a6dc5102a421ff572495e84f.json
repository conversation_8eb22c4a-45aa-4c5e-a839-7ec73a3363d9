{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Typography from '../Typography';\nimport { getDialogContentTextUtilityClass } from './dialogContentTextClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  const composedClasses = composeClasses(slots, getDialogContentTextUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst DialogContentTextRoot = styled(Typography, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiDialogContentText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nconst DialogContentText = /*#__PURE__*/React.forwardRef(function DialogContentText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContentText'\n  });\n  const {\n      className\n    } = props,\n    ownerState = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentTextRoot, _extends({\n    component: \"p\",\n    variant: \"body1\",\n    color: \"text.secondary\",\n    ref: ref,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, props, {\n    classes: classes\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContentText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContentText;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "rootShouldForwardProp", "useDefaultProps", "Typography", "getDialogContentTextUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "composedClasses", "DialogContentTextRoot", "shouldForwardProp", "prop", "name", "slot", "overridesResolver", "props", "styles", "DialogContentText", "forwardRef", "inProps", "ref", "className", "component", "variant", "color", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/DialogContentText/DialogContentText.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Typography from '../Typography';\nimport { getDialogContentTextUtilityClass } from './dialogContentTextClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  const composedClasses = composeClasses(slots, getDialogContentTextUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst DialogContentTextRoot = styled(Typography, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiDialogContentText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nconst DialogContentText = /*#__PURE__*/React.forwardRef(function DialogContentText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContentText'\n  });\n  const {\n      className\n    } = props,\n    ownerState = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentTextRoot, _extends({\n    component: \"p\",\n    variant: \"body1\",\n    color: \"text.secondary\",\n    ref: ref,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, props, {\n    classes: classes\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContentText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContentText;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,MAAMC,eAAe,GAAGb,cAAc,CAACW,KAAK,EAAEN,gCAAgC,EAAEK,OAAO,CAAC;EACxF,OAAOf,QAAQ,CAAC,CAAC,CAAC,EAAEe,OAAO,EAAEG,eAAe,CAAC;AAC/C,CAAC;AACD,MAAMC,qBAAqB,GAAGb,MAAM,CAACG,UAAU,EAAE;EAC/CW,iBAAiB,EAAEC,IAAI,IAAId,qBAAqB,CAACc,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;EAC5EC,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMU,iBAAiB,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAML,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAEI,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFS;IACF,CAAC,GAAGN,KAAK;IACTX,UAAU,GAAGf,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EAC9D,MAAMc,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,qBAAqB,EAAEnB,QAAQ,CAAC;IACvDgC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,gBAAgB;IACvBJ,GAAG,EAAEA,GAAG;IACRhB,UAAU,EAAEA,UAAU;IACtBiB,SAAS,EAAE3B,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEc,SAAS;EACzC,CAAC,EAAEN,KAAK,EAAE;IACRV,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,iBAAiB,CAACW,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEpC,SAAS,CAACqC,IAAI;EACxB;AACF;AACA;EACEzB,OAAO,EAAEZ,SAAS,CAACsC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE5B,SAAS,CAACuC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAExC,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC0C,OAAO,CAAC1C,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAACsC,MAAM,EAAEtC,SAAS,CAAC4C,IAAI,CAAC,CAAC,CAAC,EAAE5C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAACsC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}