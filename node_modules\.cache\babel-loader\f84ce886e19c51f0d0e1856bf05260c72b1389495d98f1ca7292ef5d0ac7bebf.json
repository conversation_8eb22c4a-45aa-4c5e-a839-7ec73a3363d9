{"ast": null, "code": "'use client';\n\nexport { default } from './Dialog';\nexport { default as dialogClasses } from './dialogClasses';\nexport * from './dialogClasses';", "map": {"version": 3, "names": ["default", "dialogClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Dialog/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Dialog';\nexport { default as dialogClasses } from './dialogClasses';\nexport * from './dialogClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}