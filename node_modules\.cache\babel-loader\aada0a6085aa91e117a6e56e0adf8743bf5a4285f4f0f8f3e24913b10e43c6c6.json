{"ast": null, "code": "'use client';\n\nexport { default } from './TableBody';\nexport { default as tableBodyClasses } from './tableBodyClasses';\nexport * from './tableBodyClasses';", "map": {"version": 3, "names": ["default", "tableBodyClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/TableBody/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableBody';\nexport { default as tableBodyClasses } from './tableBodyClasses';\nexport * from './tableBodyClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}