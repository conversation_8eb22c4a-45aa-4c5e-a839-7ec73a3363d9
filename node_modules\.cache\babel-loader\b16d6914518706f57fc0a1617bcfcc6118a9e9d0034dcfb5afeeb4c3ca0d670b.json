{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "boxClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Box/boxClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,MAAMC,UAAU,GAAGD,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7D,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}