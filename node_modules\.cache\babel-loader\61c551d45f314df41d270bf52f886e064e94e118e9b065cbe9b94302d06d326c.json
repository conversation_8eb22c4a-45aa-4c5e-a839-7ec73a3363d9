{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"mode\", \"contrastThreshold\", \"tonalOffset\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport { darken, getContrastRatio, lighten } from '@mui/system/colorManipulator';\nimport common from '../colors/common';\nimport grey from '../colors/grey';\nimport purple from '../colors/purple';\nimport red from '../colors/red';\nimport orange from '../colors/orange';\nimport blue from '../colors/blue';\nimport lightBlue from '../colors/lightBlue';\nimport green from '../colors/green';\nexport const light = {\n  // The colors used to style the text.\n  text: {\n    // The most important text.\n    primary: 'rgba(0, 0, 0, 0.87)',\n    // Secondary text.\n    secondary: 'rgba(0, 0, 0, 0.6)',\n    // Disabled text have even lower visual prominence.\n    disabled: 'rgba(0, 0, 0, 0.38)'\n  },\n  // The color used to divide different elements.\n  divider: 'rgba(0, 0, 0, 0.12)',\n  // The background colors used to style the surfaces.\n  // Consistency between these values is important.\n  background: {\n    paper: common.white,\n    default: common.white\n  },\n  // The colors used to style the action elements.\n  action: {\n    // The color of an active action like an icon button.\n    active: 'rgba(0, 0, 0, 0.54)',\n    // The color of an hovered action.\n    hover: 'rgba(0, 0, 0, 0.04)',\n    hoverOpacity: 0.04,\n    // The color of a selected action.\n    selected: 'rgba(0, 0, 0, 0.08)',\n    selectedOpacity: 0.08,\n    // The color of a disabled action.\n    disabled: 'rgba(0, 0, 0, 0.26)',\n    // The background color of a disabled action.\n    disabledBackground: 'rgba(0, 0, 0, 0.12)',\n    disabledOpacity: 0.38,\n    focus: 'rgba(0, 0, 0, 0.12)',\n    focusOpacity: 0.12,\n    activatedOpacity: 0.12\n  }\n};\nexport const dark = {\n  text: {\n    primary: common.white,\n    secondary: 'rgba(255, 255, 255, 0.7)',\n    disabled: 'rgba(255, 255, 255, 0.5)',\n    icon: 'rgba(255, 255, 255, 0.5)'\n  },\n  divider: 'rgba(255, 255, 255, 0.12)',\n  background: {\n    paper: '#121212',\n    default: '#121212'\n  },\n  action: {\n    active: common.white,\n    hover: 'rgba(255, 255, 255, 0.08)',\n    hoverOpacity: 0.08,\n    selected: 'rgba(255, 255, 255, 0.16)',\n    selectedOpacity: 0.16,\n    disabled: 'rgba(255, 255, 255, 0.3)',\n    disabledBackground: 'rgba(255, 255, 255, 0.12)',\n    disabledOpacity: 0.38,\n    focus: 'rgba(255, 255, 255, 0.12)',\n    focusOpacity: 0.12,\n    activatedOpacity: 0.24\n  }\n};\nfunction addLightOrDark(intent, direction, shade, tonalOffset) {\n  const tonalOffsetLight = tonalOffset.light || tonalOffset;\n  const tonalOffsetDark = tonalOffset.dark || tonalOffset * 1.5;\n  if (!intent[direction]) {\n    if (intent.hasOwnProperty(shade)) {\n      intent[direction] = intent[shade];\n    } else if (direction === 'light') {\n      intent.light = lighten(intent.main, tonalOffsetLight);\n    } else if (direction === 'dark') {\n      intent.dark = darken(intent.main, tonalOffsetDark);\n    }\n  }\n}\nfunction getDefaultPrimary(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: blue[200],\n      light: blue[50],\n      dark: blue[400]\n    };\n  }\n  return {\n    main: blue[700],\n    light: blue[400],\n    dark: blue[800]\n  };\n}\nfunction getDefaultSecondary(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: purple[200],\n      light: purple[50],\n      dark: purple[400]\n    };\n  }\n  return {\n    main: purple[500],\n    light: purple[300],\n    dark: purple[700]\n  };\n}\nfunction getDefaultError(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: red[500],\n      light: red[300],\n      dark: red[700]\n    };\n  }\n  return {\n    main: red[700],\n    light: red[400],\n    dark: red[800]\n  };\n}\nfunction getDefaultInfo(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: lightBlue[400],\n      light: lightBlue[300],\n      dark: lightBlue[700]\n    };\n  }\n  return {\n    main: lightBlue[700],\n    light: lightBlue[500],\n    dark: lightBlue[900]\n  };\n}\nfunction getDefaultSuccess(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: green[400],\n      light: green[300],\n      dark: green[700]\n    };\n  }\n  return {\n    main: green[800],\n    light: green[500],\n    dark: green[900]\n  };\n}\nfunction getDefaultWarning(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: orange[400],\n      light: orange[300],\n      dark: orange[700]\n    };\n  }\n  return {\n    main: '#ed6c02',\n    // closest to orange[800] that pass 3:1.\n    light: orange[500],\n    dark: orange[900]\n  };\n}\nexport default function createPalette(palette) {\n  const {\n      mode = 'light',\n      contrastThreshold = 3,\n      tonalOffset = 0.2\n    } = palette,\n    other = _objectWithoutPropertiesLoose(palette, _excluded);\n  const primary = palette.primary || getDefaultPrimary(mode);\n  const secondary = palette.secondary || getDefaultSecondary(mode);\n  const error = palette.error || getDefaultError(mode);\n  const info = palette.info || getDefaultInfo(mode);\n  const success = palette.success || getDefaultSuccess(mode);\n  const warning = palette.warning || getDefaultWarning(mode);\n\n  // Use the same logic as\n  // Bootstrap: https://github.com/twbs/bootstrap/blob/1d6e3710dd447de1a200f29e8fa521f8a0908f70/scss/_functions.scss#L59\n  // and material-components-web https://github.com/material-components/material-components-web/blob/ac46b8863c4dab9fc22c4c662dc6bd1b65dd652f/packages/mdc-theme/_functions.scss#L54\n  function getContrastText(background) {\n    const contrastText = getContrastRatio(background, dark.text.primary) >= contrastThreshold ? dark.text.primary : light.text.primary;\n    if (process.env.NODE_ENV !== 'production') {\n      const contrast = getContrastRatio(background, contrastText);\n      if (contrast < 3) {\n        console.error([`MUI: The contrast ratio of ${contrast}:1 for ${contrastText} on ${background}`, 'falls below the WCAG recommended absolute minimum contrast ratio of 3:1.', 'https://www.w3.org/TR/2008/REC-WCAG20-20081211/#visual-audio-contrast-contrast'].join('\\n'));\n      }\n    }\n    return contrastText;\n  }\n  const augmentColor = ({\n    color,\n    name,\n    mainShade = 500,\n    lightShade = 300,\n    darkShade = 700\n  }) => {\n    color = _extends({}, color);\n    if (!color.main && color[mainShade]) {\n      color.main = color[mainShade];\n    }\n    if (!color.hasOwnProperty('main')) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The color${name ? ` (${name})` : ''} provided to augmentColor(color) is invalid.\nThe color object needs to have a \\`main\\` property or a \\`${mainShade}\\` property.` : _formatMuiErrorMessage(11, name ? ` (${name})` : '', mainShade));\n    }\n    if (typeof color.main !== 'string') {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The color${name ? ` (${name})` : ''} provided to augmentColor(color) is invalid.\n\\`color.main\\` should be a string, but \\`${JSON.stringify(color.main)}\\` was provided instead.\n\nDid you intend to use one of the following approaches?\n\nimport { green } from \"@mui/material/colors\";\n\nconst theme1 = createTheme({ palette: {\n  primary: green,\n} });\n\nconst theme2 = createTheme({ palette: {\n  primary: { main: green[500] },\n} });` : _formatMuiErrorMessage(12, name ? ` (${name})` : '', JSON.stringify(color.main)));\n    }\n    addLightOrDark(color, 'light', lightShade, tonalOffset);\n    addLightOrDark(color, 'dark', darkShade, tonalOffset);\n    if (!color.contrastText) {\n      color.contrastText = getContrastText(color.main);\n    }\n    return color;\n  };\n  const modes = {\n    dark,\n    light\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    if (!modes[mode]) {\n      console.error(`MUI: The palette mode \\`${mode}\\` is not supported.`);\n    }\n  }\n  const paletteOutput = deepmerge(_extends({\n    // A collection of common colors.\n    common: _extends({}, common),\n    // prevent mutable object.\n    // The palette mode, can be light or dark.\n    mode,\n    // The colors used to represent primary interface elements for a user.\n    primary: augmentColor({\n      color: primary,\n      name: 'primary'\n    }),\n    // The colors used to represent secondary interface elements for a user.\n    secondary: augmentColor({\n      color: secondary,\n      name: 'secondary',\n      mainShade: 'A400',\n      lightShade: 'A200',\n      darkShade: 'A700'\n    }),\n    // The colors used to represent interface elements that the user should be made aware of.\n    error: augmentColor({\n      color: error,\n      name: 'error'\n    }),\n    // The colors used to represent potentially dangerous actions or important messages.\n    warning: augmentColor({\n      color: warning,\n      name: 'warning'\n    }),\n    // The colors used to present information to the user that is neutral and not necessarily important.\n    info: augmentColor({\n      color: info,\n      name: 'info'\n    }),\n    // The colors used to indicate the successful completion of an action that user triggered.\n    success: augmentColor({\n      color: success,\n      name: 'success'\n    }),\n    // The grey colors.\n    grey,\n    // Used by `getContrastText()` to maximize the contrast between\n    // the background and the text.\n    contrastThreshold,\n    // Takes a background color and returns the text color that maximizes the contrast.\n    getContrastText,\n    // Generate a rich color object.\n    augmentColor,\n    // Used by the functions below to shift a color's luminance by approximately\n    // two indexes within its tonal palette.\n    // E.g., shift from Red 500 to Red 300 or Red 700.\n    tonalOffset\n  }, modes[mode]), other);\n  return paletteOutput;\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_formatMuiErrorMessage", "_excluded", "deepmerge", "darken", "getContrastRatio", "lighten", "common", "grey", "purple", "red", "orange", "blue", "lightBlue", "green", "light", "text", "primary", "secondary", "disabled", "divider", "background", "paper", "white", "default", "action", "active", "hover", "hoverOpacity", "selected", "selectedOpacity", "disabledBackground", "disabledOpacity", "focus", "focusOpacity", "activatedOpacity", "dark", "icon", "addLightOrDark", "intent", "direction", "shade", "tonalOffset", "tonalOffsetLight", "tonalOffsetDark", "hasOwnProperty", "main", "getDefaultPrimary", "mode", "getDefaultSecondary", "getDefaultError", "getDefaultInfo", "getDefaultSuccess", "getDefaultWarning", "createPalette", "palette", "contrastThreshold", "other", "error", "info", "success", "warning", "getContrastText", "contrastText", "process", "env", "NODE_ENV", "contrast", "console", "join", "augmentColor", "color", "name", "mainShade", "lightShade", "darkShade", "Error", "JSON", "stringify", "modes", "paletteOutput"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/styles/createPalette.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"mode\", \"contrastThreshold\", \"tonalOffset\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport { darken, getContrastRatio, lighten } from '@mui/system/colorManipulator';\nimport common from '../colors/common';\nimport grey from '../colors/grey';\nimport purple from '../colors/purple';\nimport red from '../colors/red';\nimport orange from '../colors/orange';\nimport blue from '../colors/blue';\nimport lightBlue from '../colors/lightBlue';\nimport green from '../colors/green';\nexport const light = {\n  // The colors used to style the text.\n  text: {\n    // The most important text.\n    primary: 'rgba(0, 0, 0, 0.87)',\n    // Secondary text.\n    secondary: 'rgba(0, 0, 0, 0.6)',\n    // Disabled text have even lower visual prominence.\n    disabled: 'rgba(0, 0, 0, 0.38)'\n  },\n  // The color used to divide different elements.\n  divider: 'rgba(0, 0, 0, 0.12)',\n  // The background colors used to style the surfaces.\n  // Consistency between these values is important.\n  background: {\n    paper: common.white,\n    default: common.white\n  },\n  // The colors used to style the action elements.\n  action: {\n    // The color of an active action like an icon button.\n    active: 'rgba(0, 0, 0, 0.54)',\n    // The color of an hovered action.\n    hover: 'rgba(0, 0, 0, 0.04)',\n    hoverOpacity: 0.04,\n    // The color of a selected action.\n    selected: 'rgba(0, 0, 0, 0.08)',\n    selectedOpacity: 0.08,\n    // The color of a disabled action.\n    disabled: 'rgba(0, 0, 0, 0.26)',\n    // The background color of a disabled action.\n    disabledBackground: 'rgba(0, 0, 0, 0.12)',\n    disabledOpacity: 0.38,\n    focus: 'rgba(0, 0, 0, 0.12)',\n    focusOpacity: 0.12,\n    activatedOpacity: 0.12\n  }\n};\nexport const dark = {\n  text: {\n    primary: common.white,\n    secondary: 'rgba(255, 255, 255, 0.7)',\n    disabled: 'rgba(255, 255, 255, 0.5)',\n    icon: 'rgba(255, 255, 255, 0.5)'\n  },\n  divider: 'rgba(255, 255, 255, 0.12)',\n  background: {\n    paper: '#121212',\n    default: '#121212'\n  },\n  action: {\n    active: common.white,\n    hover: 'rgba(255, 255, 255, 0.08)',\n    hoverOpacity: 0.08,\n    selected: 'rgba(255, 255, 255, 0.16)',\n    selectedOpacity: 0.16,\n    disabled: 'rgba(255, 255, 255, 0.3)',\n    disabledBackground: 'rgba(255, 255, 255, 0.12)',\n    disabledOpacity: 0.38,\n    focus: 'rgba(255, 255, 255, 0.12)',\n    focusOpacity: 0.12,\n    activatedOpacity: 0.24\n  }\n};\nfunction addLightOrDark(intent, direction, shade, tonalOffset) {\n  const tonalOffsetLight = tonalOffset.light || tonalOffset;\n  const tonalOffsetDark = tonalOffset.dark || tonalOffset * 1.5;\n  if (!intent[direction]) {\n    if (intent.hasOwnProperty(shade)) {\n      intent[direction] = intent[shade];\n    } else if (direction === 'light') {\n      intent.light = lighten(intent.main, tonalOffsetLight);\n    } else if (direction === 'dark') {\n      intent.dark = darken(intent.main, tonalOffsetDark);\n    }\n  }\n}\nfunction getDefaultPrimary(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: blue[200],\n      light: blue[50],\n      dark: blue[400]\n    };\n  }\n  return {\n    main: blue[700],\n    light: blue[400],\n    dark: blue[800]\n  };\n}\nfunction getDefaultSecondary(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: purple[200],\n      light: purple[50],\n      dark: purple[400]\n    };\n  }\n  return {\n    main: purple[500],\n    light: purple[300],\n    dark: purple[700]\n  };\n}\nfunction getDefaultError(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: red[500],\n      light: red[300],\n      dark: red[700]\n    };\n  }\n  return {\n    main: red[700],\n    light: red[400],\n    dark: red[800]\n  };\n}\nfunction getDefaultInfo(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: lightBlue[400],\n      light: lightBlue[300],\n      dark: lightBlue[700]\n    };\n  }\n  return {\n    main: lightBlue[700],\n    light: lightBlue[500],\n    dark: lightBlue[900]\n  };\n}\nfunction getDefaultSuccess(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: green[400],\n      light: green[300],\n      dark: green[700]\n    };\n  }\n  return {\n    main: green[800],\n    light: green[500],\n    dark: green[900]\n  };\n}\nfunction getDefaultWarning(mode = 'light') {\n  if (mode === 'dark') {\n    return {\n      main: orange[400],\n      light: orange[300],\n      dark: orange[700]\n    };\n  }\n  return {\n    main: '#ed6c02',\n    // closest to orange[800] that pass 3:1.\n    light: orange[500],\n    dark: orange[900]\n  };\n}\nexport default function createPalette(palette) {\n  const {\n      mode = 'light',\n      contrastThreshold = 3,\n      tonalOffset = 0.2\n    } = palette,\n    other = _objectWithoutPropertiesLoose(palette, _excluded);\n  const primary = palette.primary || getDefaultPrimary(mode);\n  const secondary = palette.secondary || getDefaultSecondary(mode);\n  const error = palette.error || getDefaultError(mode);\n  const info = palette.info || getDefaultInfo(mode);\n  const success = palette.success || getDefaultSuccess(mode);\n  const warning = palette.warning || getDefaultWarning(mode);\n\n  // Use the same logic as\n  // Bootstrap: https://github.com/twbs/bootstrap/blob/1d6e3710dd447de1a200f29e8fa521f8a0908f70/scss/_functions.scss#L59\n  // and material-components-web https://github.com/material-components/material-components-web/blob/ac46b8863c4dab9fc22c4c662dc6bd1b65dd652f/packages/mdc-theme/_functions.scss#L54\n  function getContrastText(background) {\n    const contrastText = getContrastRatio(background, dark.text.primary) >= contrastThreshold ? dark.text.primary : light.text.primary;\n    if (process.env.NODE_ENV !== 'production') {\n      const contrast = getContrastRatio(background, contrastText);\n      if (contrast < 3) {\n        console.error([`MUI: The contrast ratio of ${contrast}:1 for ${contrastText} on ${background}`, 'falls below the WCAG recommended absolute minimum contrast ratio of 3:1.', 'https://www.w3.org/TR/2008/REC-WCAG20-20081211/#visual-audio-contrast-contrast'].join('\\n'));\n      }\n    }\n    return contrastText;\n  }\n  const augmentColor = ({\n    color,\n    name,\n    mainShade = 500,\n    lightShade = 300,\n    darkShade = 700\n  }) => {\n    color = _extends({}, color);\n    if (!color.main && color[mainShade]) {\n      color.main = color[mainShade];\n    }\n    if (!color.hasOwnProperty('main')) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The color${name ? ` (${name})` : ''} provided to augmentColor(color) is invalid.\nThe color object needs to have a \\`main\\` property or a \\`${mainShade}\\` property.` : _formatMuiErrorMessage(11, name ? ` (${name})` : '', mainShade));\n    }\n    if (typeof color.main !== 'string') {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The color${name ? ` (${name})` : ''} provided to augmentColor(color) is invalid.\n\\`color.main\\` should be a string, but \\`${JSON.stringify(color.main)}\\` was provided instead.\n\nDid you intend to use one of the following approaches?\n\nimport { green } from \"@mui/material/colors\";\n\nconst theme1 = createTheme({ palette: {\n  primary: green,\n} });\n\nconst theme2 = createTheme({ palette: {\n  primary: { main: green[500] },\n} });` : _formatMuiErrorMessage(12, name ? ` (${name})` : '', JSON.stringify(color.main)));\n    }\n    addLightOrDark(color, 'light', lightShade, tonalOffset);\n    addLightOrDark(color, 'dark', darkShade, tonalOffset);\n    if (!color.contrastText) {\n      color.contrastText = getContrastText(color.main);\n    }\n    return color;\n  };\n  const modes = {\n    dark,\n    light\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    if (!modes[mode]) {\n      console.error(`MUI: The palette mode \\`${mode}\\` is not supported.`);\n    }\n  }\n  const paletteOutput = deepmerge(_extends({\n    // A collection of common colors.\n    common: _extends({}, common),\n    // prevent mutable object.\n    // The palette mode, can be light or dark.\n    mode,\n    // The colors used to represent primary interface elements for a user.\n    primary: augmentColor({\n      color: primary,\n      name: 'primary'\n    }),\n    // The colors used to represent secondary interface elements for a user.\n    secondary: augmentColor({\n      color: secondary,\n      name: 'secondary',\n      mainShade: 'A400',\n      lightShade: 'A200',\n      darkShade: 'A700'\n    }),\n    // The colors used to represent interface elements that the user should be made aware of.\n    error: augmentColor({\n      color: error,\n      name: 'error'\n    }),\n    // The colors used to represent potentially dangerous actions or important messages.\n    warning: augmentColor({\n      color: warning,\n      name: 'warning'\n    }),\n    // The colors used to present information to the user that is neutral and not necessarily important.\n    info: augmentColor({\n      color: info,\n      name: 'info'\n    }),\n    // The colors used to indicate the successful completion of an action that user triggered.\n    success: augmentColor({\n      color: success,\n      name: 'success'\n    }),\n    // The grey colors.\n    grey,\n    // Used by `getContrastText()` to maximize the contrast between\n    // the background and the text.\n    contrastThreshold,\n    // Takes a background color and returns the text color that maximizes the contrast.\n    getContrastText,\n    // Generate a rich color object.\n    augmentColor,\n    // Used by the functions below to shift a color's luminance by approximately\n    // two indexes within its tonal palette.\n    // E.g., shift from Red 500 to Red 300 or Red 700.\n    tonalOffset\n  }, modes[mode]), other);\n  return paletteOutput;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,mBAAmB,EAAE,aAAa,CAAC;AAC9D,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,8BAA8B;AAChF,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,MAAMC,KAAK,GAAG;EACnB;EACAC,IAAI,EAAE;IACJ;IACAC,OAAO,EAAE,qBAAqB;IAC9B;IACAC,SAAS,EAAE,oBAAoB;IAC/B;IACAC,QAAQ,EAAE;EACZ,CAAC;EACD;EACAC,OAAO,EAAE,qBAAqB;EAC9B;EACA;EACAC,UAAU,EAAE;IACVC,KAAK,EAAEf,MAAM,CAACgB,KAAK;IACnBC,OAAO,EAAEjB,MAAM,CAACgB;EAClB,CAAC;EACD;EACAE,MAAM,EAAE;IACN;IACAC,MAAM,EAAE,qBAAqB;IAC7B;IACAC,KAAK,EAAE,qBAAqB;IAC5BC,YAAY,EAAE,IAAI;IAClB;IACAC,QAAQ,EAAE,qBAAqB;IAC/BC,eAAe,EAAE,IAAI;IACrB;IACAX,QAAQ,EAAE,qBAAqB;IAC/B;IACAY,kBAAkB,EAAE,qBAAqB;IACzCC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,qBAAqB;IAC5BC,YAAY,EAAE,IAAI;IAClBC,gBAAgB,EAAE;EACpB;AACF,CAAC;AACD,OAAO,MAAMC,IAAI,GAAG;EAClBpB,IAAI,EAAE;IACJC,OAAO,EAAEV,MAAM,CAACgB,KAAK;IACrBL,SAAS,EAAE,0BAA0B;IACrCC,QAAQ,EAAE,0BAA0B;IACpCkB,IAAI,EAAE;EACR,CAAC;EACDjB,OAAO,EAAE,2BAA2B;EACpCC,UAAU,EAAE;IACVC,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE;EACX,CAAC;EACDC,MAAM,EAAE;IACNC,MAAM,EAAEnB,MAAM,CAACgB,KAAK;IACpBI,KAAK,EAAE,2BAA2B;IAClCC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,2BAA2B;IACrCC,eAAe,EAAE,IAAI;IACrBX,QAAQ,EAAE,0BAA0B;IACpCY,kBAAkB,EAAE,2BAA2B;IAC/CC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,YAAY,EAAE,IAAI;IAClBC,gBAAgB,EAAE;EACpB;AACF,CAAC;AACD,SAASG,cAAcA,CAACC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC7D,MAAMC,gBAAgB,GAAGD,WAAW,CAAC3B,KAAK,IAAI2B,WAAW;EACzD,MAAME,eAAe,GAAGF,WAAW,CAACN,IAAI,IAAIM,WAAW,GAAG,GAAG;EAC7D,IAAI,CAACH,MAAM,CAACC,SAAS,CAAC,EAAE;IACtB,IAAID,MAAM,CAACM,cAAc,CAACJ,KAAK,CAAC,EAAE;MAChCF,MAAM,CAACC,SAAS,CAAC,GAAGD,MAAM,CAACE,KAAK,CAAC;IACnC,CAAC,MAAM,IAAID,SAAS,KAAK,OAAO,EAAE;MAChCD,MAAM,CAACxB,KAAK,GAAGT,OAAO,CAACiC,MAAM,CAACO,IAAI,EAAEH,gBAAgB,CAAC;IACvD,CAAC,MAAM,IAAIH,SAAS,KAAK,MAAM,EAAE;MAC/BD,MAAM,CAACH,IAAI,GAAGhC,MAAM,CAACmC,MAAM,CAACO,IAAI,EAAEF,eAAe,CAAC;IACpD;EACF;AACF;AACA,SAASG,iBAAiBA,CAACC,IAAI,GAAG,OAAO,EAAE;EACzC,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAElC,IAAI,CAAC,GAAG,CAAC;MACfG,KAAK,EAAEH,IAAI,CAAC,EAAE,CAAC;MACfwB,IAAI,EAAExB,IAAI,CAAC,GAAG;IAChB,CAAC;EACH;EACA,OAAO;IACLkC,IAAI,EAAElC,IAAI,CAAC,GAAG,CAAC;IACfG,KAAK,EAAEH,IAAI,CAAC,GAAG,CAAC;IAChBwB,IAAI,EAAExB,IAAI,CAAC,GAAG;EAChB,CAAC;AACH;AACA,SAASqC,mBAAmBA,CAACD,IAAI,GAAG,OAAO,EAAE;EAC3C,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAErC,MAAM,CAAC,GAAG,CAAC;MACjBM,KAAK,EAAEN,MAAM,CAAC,EAAE,CAAC;MACjB2B,IAAI,EAAE3B,MAAM,CAAC,GAAG;IAClB,CAAC;EACH;EACA,OAAO;IACLqC,IAAI,EAAErC,MAAM,CAAC,GAAG,CAAC;IACjBM,KAAK,EAAEN,MAAM,CAAC,GAAG,CAAC;IAClB2B,IAAI,EAAE3B,MAAM,CAAC,GAAG;EAClB,CAAC;AACH;AACA,SAASyC,eAAeA,CAACF,IAAI,GAAG,OAAO,EAAE;EACvC,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAEpC,GAAG,CAAC,GAAG,CAAC;MACdK,KAAK,EAAEL,GAAG,CAAC,GAAG,CAAC;MACf0B,IAAI,EAAE1B,GAAG,CAAC,GAAG;IACf,CAAC;EACH;EACA,OAAO;IACLoC,IAAI,EAAEpC,GAAG,CAAC,GAAG,CAAC;IACdK,KAAK,EAAEL,GAAG,CAAC,GAAG,CAAC;IACf0B,IAAI,EAAE1B,GAAG,CAAC,GAAG;EACf,CAAC;AACH;AACA,SAASyC,cAAcA,CAACH,IAAI,GAAG,OAAO,EAAE;EACtC,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAEjC,SAAS,CAAC,GAAG,CAAC;MACpBE,KAAK,EAAEF,SAAS,CAAC,GAAG,CAAC;MACrBuB,IAAI,EAAEvB,SAAS,CAAC,GAAG;IACrB,CAAC;EACH;EACA,OAAO;IACLiC,IAAI,EAAEjC,SAAS,CAAC,GAAG,CAAC;IACpBE,KAAK,EAAEF,SAAS,CAAC,GAAG,CAAC;IACrBuB,IAAI,EAAEvB,SAAS,CAAC,GAAG;EACrB,CAAC;AACH;AACA,SAASuC,iBAAiBA,CAACJ,IAAI,GAAG,OAAO,EAAE;EACzC,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAEhC,KAAK,CAAC,GAAG,CAAC;MAChBC,KAAK,EAAED,KAAK,CAAC,GAAG,CAAC;MACjBsB,IAAI,EAAEtB,KAAK,CAAC,GAAG;IACjB,CAAC;EACH;EACA,OAAO;IACLgC,IAAI,EAAEhC,KAAK,CAAC,GAAG,CAAC;IAChBC,KAAK,EAAED,KAAK,CAAC,GAAG,CAAC;IACjBsB,IAAI,EAAEtB,KAAK,CAAC,GAAG;EACjB,CAAC;AACH;AACA,SAASuC,iBAAiBA,CAACL,IAAI,GAAG,OAAO,EAAE;EACzC,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLF,IAAI,EAAEnC,MAAM,CAAC,GAAG,CAAC;MACjBI,KAAK,EAAEJ,MAAM,CAAC,GAAG,CAAC;MAClByB,IAAI,EAAEzB,MAAM,CAAC,GAAG;IAClB,CAAC;EACH;EACA,OAAO;IACLmC,IAAI,EAAE,SAAS;IACf;IACA/B,KAAK,EAAEJ,MAAM,CAAC,GAAG,CAAC;IAClByB,IAAI,EAAEzB,MAAM,CAAC,GAAG;EAClB,CAAC;AACH;AACA,eAAe,SAAS2C,aAAaA,CAACC,OAAO,EAAE;EAC7C,MAAM;MACFP,IAAI,GAAG,OAAO;MACdQ,iBAAiB,GAAG,CAAC;MACrBd,WAAW,GAAG;IAChB,CAAC,GAAGa,OAAO;IACXE,KAAK,GAAGzD,6BAA6B,CAACuD,OAAO,EAAErD,SAAS,CAAC;EAC3D,MAAMe,OAAO,GAAGsC,OAAO,CAACtC,OAAO,IAAI8B,iBAAiB,CAACC,IAAI,CAAC;EAC1D,MAAM9B,SAAS,GAAGqC,OAAO,CAACrC,SAAS,IAAI+B,mBAAmB,CAACD,IAAI,CAAC;EAChE,MAAMU,KAAK,GAAGH,OAAO,CAACG,KAAK,IAAIR,eAAe,CAACF,IAAI,CAAC;EACpD,MAAMW,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAIR,cAAc,CAACH,IAAI,CAAC;EACjD,MAAMY,OAAO,GAAGL,OAAO,CAACK,OAAO,IAAIR,iBAAiB,CAACJ,IAAI,CAAC;EAC1D,MAAMa,OAAO,GAAGN,OAAO,CAACM,OAAO,IAAIR,iBAAiB,CAACL,IAAI,CAAC;;EAE1D;EACA;EACA;EACA,SAASc,eAAeA,CAACzC,UAAU,EAAE;IACnC,MAAM0C,YAAY,GAAG1D,gBAAgB,CAACgB,UAAU,EAAEe,IAAI,CAACpB,IAAI,CAACC,OAAO,CAAC,IAAIuC,iBAAiB,GAAGpB,IAAI,CAACpB,IAAI,CAACC,OAAO,GAAGF,KAAK,CAACC,IAAI,CAACC,OAAO;IAClI,IAAI+C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMC,QAAQ,GAAG9D,gBAAgB,CAACgB,UAAU,EAAE0C,YAAY,CAAC;MAC3D,IAAII,QAAQ,GAAG,CAAC,EAAE;QAChBC,OAAO,CAACV,KAAK,CAAC,CAAC,8BAA8BS,QAAQ,UAAUJ,YAAY,OAAO1C,UAAU,EAAE,EAAE,0EAA0E,EAAE,gFAAgF,CAAC,CAACgD,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3Q;IACF;IACA,OAAON,YAAY;EACrB;EACA,MAAMO,YAAY,GAAGA,CAAC;IACpBC,KAAK;IACLC,IAAI;IACJC,SAAS,GAAG,GAAG;IACfC,UAAU,GAAG,GAAG;IAChBC,SAAS,GAAG;EACd,CAAC,KAAK;IACJJ,KAAK,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEwE,KAAK,CAAC;IAC3B,IAAI,CAACA,KAAK,CAACzB,IAAI,IAAIyB,KAAK,CAACE,SAAS,CAAC,EAAE;MACnCF,KAAK,CAACzB,IAAI,GAAGyB,KAAK,CAACE,SAAS,CAAC;IAC/B;IACA,IAAI,CAACF,KAAK,CAAC1B,cAAc,CAAC,MAAM,CAAC,EAAE;MACjC,MAAM,IAAI+B,KAAK,CAACZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,iBAAiBM,IAAI,GAAG,KAAKA,IAAI,GAAG,GAAG,EAAE;AACvG,4DAA4DC,SAAS,cAAc,GAAGxE,sBAAsB,CAAC,EAAE,EAAEuE,IAAI,GAAG,KAAKA,IAAI,GAAG,GAAG,EAAE,EAAEC,SAAS,CAAC,CAAC;IAClJ;IACA,IAAI,OAAOF,KAAK,CAACzB,IAAI,KAAK,QAAQ,EAAE;MAClC,MAAM,IAAI8B,KAAK,CAACZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,iBAAiBM,IAAI,GAAG,KAAKA,IAAI,GAAG,GAAG,EAAE;AACvG,2CAA2CK,IAAI,CAACC,SAAS,CAACP,KAAK,CAACzB,IAAI,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,GAAG7C,sBAAsB,CAAC,EAAE,EAAEuE,IAAI,GAAG,KAAKA,IAAI,GAAG,GAAG,EAAE,EAAEK,IAAI,CAACC,SAAS,CAACP,KAAK,CAACzB,IAAI,CAAC,CAAC,CAAC;IACtF;IACAR,cAAc,CAACiC,KAAK,EAAE,OAAO,EAAEG,UAAU,EAAEhC,WAAW,CAAC;IACvDJ,cAAc,CAACiC,KAAK,EAAE,MAAM,EAAEI,SAAS,EAAEjC,WAAW,CAAC;IACrD,IAAI,CAAC6B,KAAK,CAACR,YAAY,EAAE;MACvBQ,KAAK,CAACR,YAAY,GAAGD,eAAe,CAACS,KAAK,CAACzB,IAAI,CAAC;IAClD;IACA,OAAOyB,KAAK;EACd,CAAC;EACD,MAAMQ,KAAK,GAAG;IACZ3C,IAAI;IACJrB;EACF,CAAC;EACD,IAAIiD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACa,KAAK,CAAC/B,IAAI,CAAC,EAAE;MAChBoB,OAAO,CAACV,KAAK,CAAC,2BAA2BV,IAAI,sBAAsB,CAAC;IACtE;EACF;EACA,MAAMgC,aAAa,GAAG7E,SAAS,CAACJ,QAAQ,CAAC;IACvC;IACAQ,MAAM,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEQ,MAAM,CAAC;IAC5B;IACA;IACAyC,IAAI;IACJ;IACA/B,OAAO,EAAEqD,YAAY,CAAC;MACpBC,KAAK,EAAEtD,OAAO;MACduD,IAAI,EAAE;IACR,CAAC,CAAC;IACF;IACAtD,SAAS,EAAEoD,YAAY,CAAC;MACtBC,KAAK,EAAErD,SAAS;MAChBsD,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAjB,KAAK,EAAEY,YAAY,CAAC;MAClBC,KAAK,EAAEb,KAAK;MACZc,IAAI,EAAE;IACR,CAAC,CAAC;IACF;IACAX,OAAO,EAAES,YAAY,CAAC;MACpBC,KAAK,EAAEV,OAAO;MACdW,IAAI,EAAE;IACR,CAAC,CAAC;IACF;IACAb,IAAI,EAAEW,YAAY,CAAC;MACjBC,KAAK,EAAEZ,IAAI;MACXa,IAAI,EAAE;IACR,CAAC,CAAC;IACF;IACAZ,OAAO,EAAEU,YAAY,CAAC;MACpBC,KAAK,EAAEX,OAAO;MACdY,IAAI,EAAE;IACR,CAAC,CAAC;IACF;IACAhE,IAAI;IACJ;IACA;IACAgD,iBAAiB;IACjB;IACAM,eAAe;IACf;IACAQ,YAAY;IACZ;IACA;IACA;IACA5B;EACF,CAAC,EAAEqC,KAAK,CAAC/B,IAAI,CAAC,CAAC,EAAES,KAAK,CAAC;EACvB,OAAOuB,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}