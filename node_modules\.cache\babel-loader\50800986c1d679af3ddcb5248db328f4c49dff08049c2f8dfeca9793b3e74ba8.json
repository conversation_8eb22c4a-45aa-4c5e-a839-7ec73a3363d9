{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"disableSpacing\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogActionsUtilityClass } from './dialogActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getDialogActionsUtilityClass, classes);\n};\nconst DialogActionsRoot = styled('div', {\n  name: 'MuiDialogActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  flex: '0 0 auto'\n}, !ownerState.disableSpacing && {\n  '& > :not(style) ~ :not(style)': {\n    marginLeft: 8\n  }\n}));\nconst DialogActions = /*#__PURE__*/React.forwardRef(function DialogActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogActions'\n  });\n  const {\n      className,\n      disableSpacing = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogActions;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getDialogActionsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "disableSpacing", "slots", "root", "DialogActionsRoot", "name", "slot", "overridesResolver", "props", "styles", "spacing", "display", "alignItems", "padding", "justifyContent", "flex", "marginLeft", "DialogActions", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/DialogActions/DialogActions.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"disableSpacing\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogActionsUtilityClass } from './dialogActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getDialogActionsUtilityClass, classes);\n};\nconst DialogActionsRoot = styled('div', {\n  name: 'MuiDialogActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  flex: '0 0 auto'\n}, !ownerState.disableSpacing && {\n  '& > :not(style) ~ :not(style)': {\n    marginLeft: 8\n  }\n}));\nconst DialogActions = /*#__PURE__*/React.forwardRef(function DialogActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogActions'\n  });\n  const {\n      className,\n      disableSpacing = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogActions;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACF,cAAc,IAAI,SAAS;EAC7C,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEP,4BAA4B,EAAEK,OAAO,CAAC;AACrE,CAAC;AACD,MAAMI,iBAAiB,GAAGX,MAAM,CAAC,KAAK,EAAE;EACtCY,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAE,CAACJ,UAAU,CAACE,cAAc,IAAIQ,MAAM,CAACC,OAAO,CAAC;EACpE;AACF,CAAC,CAAC,CAAC,CAAC;EACFX;AACF,CAAC,KAAKZ,QAAQ,CAAC;EACbwB,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,UAAU;EAC1BC,IAAI,EAAE;AACR,CAAC,EAAE,CAAChB,UAAU,CAACE,cAAc,IAAI;EAC/B,+BAA+B,EAAE;IAC/Be,UAAU,EAAE;EACd;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMZ,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEW,OAAO;IACdd,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFgB,SAAS;MACTpB,cAAc,GAAG;IACnB,CAAC,GAAGO,KAAK;IACTc,KAAK,GAAGpC,6BAA6B,CAACsB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMW,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCP;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,iBAAiB,EAAEjB,QAAQ,CAAC;IACnDkC,SAAS,EAAE9B,IAAI,CAACS,OAAO,CAACG,IAAI,EAAEkB,SAAS,CAAC;IACxCtB,UAAU,EAAEA,UAAU;IACtBqB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,aAAa,CAACS,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAErC,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEV,SAAS,CAACuC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAE/B,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;AACA;EACE7B,cAAc,EAAEX,SAAS,CAACyC,IAAI;EAC9B;AACF;AACA;EACEC,EAAE,EAAE1C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,OAAO,CAAC5C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,EAAEvC,SAAS,CAACyC,IAAI,CAAC,CAAC,CAAC,EAAEzC,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}