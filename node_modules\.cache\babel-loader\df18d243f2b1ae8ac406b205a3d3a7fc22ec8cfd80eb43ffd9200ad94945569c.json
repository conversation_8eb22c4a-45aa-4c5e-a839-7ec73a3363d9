{"ast": null, "code": "'use client';\n\nexport { default } from './Hidden';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Hidden/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Hidden';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}