{"ast": null, "code": "import listCacheClear from './_listCacheClear.js';\nimport listCacheDelete from './_listCacheDelete.js';\nimport listCacheGet from './_listCacheGet.js';\nimport listCacheHas from './_listCacheHas.js';\nimport listCacheSet from './_listCacheSet.js';\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\nexport default ListCache;", "map": {"version": 3, "names": ["listCacheClear", "listCacheDelete", "listCacheGet", "listCacheHas", "listCacheSet", "ListCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/lodash-es/_ListCache.js"], "sourcesContent": ["import listCacheClear from './_listCacheClear.js';\nimport listCacheDelete from './_listCacheDelete.js';\nimport listCacheGet from './_listCacheGet.js';\nimport listCacheHas from './_listCacheHas.js';\nimport listCacheSet from './_listCacheSet.js';\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\nexport default ListCache;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,SAAS,CAACO,SAAS,CAACH,KAAK,GAAGT,cAAc;AAC1CK,SAAS,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,eAAe;AAC/CI,SAAS,CAACO,SAAS,CAACC,GAAG,GAAGX,YAAY;AACtCG,SAAS,CAACO,SAAS,CAACE,GAAG,GAAGX,YAAY;AACtCE,SAAS,CAACO,SAAS,CAACD,GAAG,GAAGP,YAAY;AAEtC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}