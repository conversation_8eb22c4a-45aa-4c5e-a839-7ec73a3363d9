{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableFooterUtilityClass(slot) {\n  return generateUtilityClass('MuiTableFooter', slot);\n}\nconst tableFooterClasses = generateUtilityClasses('MuiTableFooter', ['root']);\nexport default tableFooterClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableFooterUtilityClass", "slot", "tableFooterClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/TableFooter/tableFooterClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableFooterUtilityClass(slot) {\n  return generateUtilityClass('MuiTableFooter', slot);\n}\nconst tableFooterClasses = generateUtilityClasses('MuiTableFooter', ['root']);\nexport default tableFooterClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7E,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}