{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabScrollButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiTabScrollButton', slot);\n}\nconst tabScrollButtonClasses = generateUtilityClasses('MuiTabScrollButton', ['root', 'vertical', 'horizontal', 'disabled']);\nexport default tabScrollButtonClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTabScrollButtonUtilityClass", "slot", "tabScrollButtonClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/TabScrollButton/tabScrollButtonClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabScrollButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiTabScrollButton', slot);\n}\nconst tabScrollButtonClasses = generateUtilityClasses('MuiTabScrollButton', ['root', 'vertical', 'horizontal', 'disabled']);\nexport default tabScrollButtonClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOF,oBAAoB,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACzD;AACA,MAAMC,sBAAsB,GAAGJ,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC3H,eAAeI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}