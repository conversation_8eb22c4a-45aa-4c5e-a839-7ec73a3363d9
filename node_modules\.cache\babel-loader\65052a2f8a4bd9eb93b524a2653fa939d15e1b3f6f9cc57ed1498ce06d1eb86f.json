{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\nimport { useFormik } from 'formik';\nimport * as Yup from 'yup';\nimport { Container, Paper, Typography, Button, Link, Box, CircularProgress, Snackbar, Alert } from '@mui/material';\nimport TextFieldWrapper from '../components/TextFieldWrapper';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst validationSchema = Yup.object({\n  email: Yup.string().email('Invalid email address').required('Email is required'),\n  password: Yup.string().required('Password is required')\n});\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n  const formik = useFormik({\n    initialValues: {\n      email: '',\n      password: ''\n    },\n    validationSchema,\n    onSubmit: async values => {\n      setLoading(true);\n      try {\n        await authService.login(values);\n        navigate('/profile');\n      } catch (err) {\n        setError(err.message || 'Login failed');\n        setShowError(true);\n      } finally {\n        setLoading(false);\n      }\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"xs\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4,\n        mt: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        component: \"h1\",\n        variant: \"h5\",\n        align: \"center\",\n        gutterBottom: true,\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: formik.handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"email\",\n          label: \"Email Address\",\n          value: formik.values.email,\n          onChange: formik.handleChange,\n          error: formik.touched.email && Boolean(formik.errors.email),\n          helperText: formik.touched.email && formik.errors.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"password\",\n          label: \"Password\",\n          type: \"password\",\n          value: formik.values.password,\n          onChange: formik.handleChange,\n          error: formik.touched.password && Boolean(formik.errors.password),\n          helperText: formik.touched.password && formik.errors.password\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 24\n          }, this) : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            component: RouterLink,\n            to: \"/forgot-password\",\n            variant: \"body2\",\n            children: \"Forgot password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              component: RouterLink,\n              to: \"/signup\",\n              variant: \"body2\",\n              children: \"Don't have an account? Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: () => setShowError(false),\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        onClose: () => setShowError(false),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"42+WMuy0r3blxthFAzMyQiiODVE=\", false, function () {\n  return [useNavigate, useFormik];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["useState", "useNavigate", "Link", "RouterLink", "useFormik", "<PERSON><PERSON>", "Container", "Paper", "Typography", "<PERSON><PERSON>", "Box", "CircularProgress", "Snackbar", "<PERSON><PERSON>", "TextFieldWrapper", "authService", "jsxDEV", "_jsxDEV", "validationSchema", "object", "email", "string", "required", "password", "<PERSON><PERSON>", "_s", "navigate", "loading", "setLoading", "error", "setError", "showError", "setShowError", "formik", "initialValues", "onSubmit", "values", "login", "err", "message", "component", "max<PERSON><PERSON><PERSON>", "children", "elevation", "sx", "p", "mt", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleSubmit", "name", "label", "value", "onChange", "handleChange", "touched", "Boolean", "errors", "helperText", "type", "fullWidth", "mb", "disabled", "size", "textAlign", "to", "open", "autoHideDuration", "onClose", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/pages/Login.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\r\nimport { useFormik } from 'formik';\r\nimport * as Yup from 'yup';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  Button,\r\n  Link,\r\n  Box,\r\n  CircularProgress,\r\n  Snackbar,\r\n  Alert\r\n} from '@mui/material';\r\nimport TextFieldWrapper from '../components/TextFieldWrapper';\r\nimport authService from '../api/authService';\r\n\r\nconst validationSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  password: Yup.string()\r\n    .required('Password is required')\r\n});\r\n\r\nconst Login = () => {\r\n  const navigate = useNavigate();\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n\r\n  const formik = useFormik({\r\n    initialValues: {\r\n      email: '',\r\n      password: ''\r\n    },\r\n    validationSchema,\r\n    onSubmit: async (values) => {\r\n      setLoading(true);\r\n      try {\r\n        await authService.login(values);\r\n        navigate('/profile');\r\n      } catch (err) {\r\n        setError(err.message || 'Login failed');\r\n        setShowError(true);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  });\r\n\r\n  return (\r\n    <Container component=\"main\" maxWidth=\"xs\">\r\n      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>\r\n        <Typography component=\"h1\" variant=\"h5\" align=\"center\" gutterBottom>\r\n          Login\r\n        </Typography>\r\n        <form onSubmit={formik.handleSubmit}>\r\n          <TextFieldWrapper\r\n            name=\"email\"\r\n            label=\"Email Address\"\r\n            value={formik.values.email}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.email && Boolean(formik.errors.email)}\r\n            helperText={formik.touched.email && formik.errors.email}\r\n          />\r\n          <TextFieldWrapper\r\n            name=\"password\"\r\n            label=\"Password\"\r\n            type=\"password\"\r\n            value={formik.values.password}\r\n            onChange={formik.handleChange}\r\n            error={formik.touched.password && Boolean(formik.errors.password)}\r\n            helperText={formik.touched.password && formik.errors.password}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            fullWidth\r\n            variant=\"contained\"\r\n            sx={{ mt: 3, mb: 2 }}\r\n            disabled={loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} /> : 'Login'}\r\n          </Button>\r\n          <Box sx={{ textAlign: 'center' }}>\r\n            <Link component={RouterLink} to=\"/forgot-password\" variant=\"body2\">\r\n              Forgot password?\r\n            </Link>\r\n            <Box sx={{ mt: 1 }}>\r\n              <Link component={RouterLink} to=\"/signup\" variant=\"body2\">\r\n                Don't have an account? Sign Up\r\n              </Link>\r\n            </Box>\r\n          </Box>\r\n        </form>\r\n      </Paper>\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={() => setShowError(false)}\r\n      >\r\n        <Alert severity=\"error\" onClose={() => setShowError(false)}>\r\n          {error}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Login; "], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,EAAEC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AAClE,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNP,IAAI,EACJQ,GAAG,EACHC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,gBAAgB,GAAGb,GAAG,CAACc,MAAM,CAAC;EAClCC,KAAK,EAAEf,GAAG,CAACgB,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,QAAQ,EAAElB,GAAG,CAACgB,MAAM,CAAC,CAAC,CACnBC,QAAQ,CAAC,sBAAsB;AACpC,CAAC,CAAC;AAEF,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMiC,MAAM,GAAG7B,SAAS,CAAC;IACvB8B,aAAa,EAAE;MACbd,KAAK,EAAE,EAAE;MACTG,QAAQ,EAAE;IACZ,CAAC;IACDL,gBAAgB;IAChBiB,QAAQ,EAAE,MAAOC,MAAM,IAAK;MAC1BR,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMb,WAAW,CAACsB,KAAK,CAACD,MAAM,CAAC;QAC/BV,QAAQ,CAAC,UAAU,CAAC;MACtB,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZR,QAAQ,CAACQ,GAAG,CAACC,OAAO,IAAI,cAAc,CAAC;QACvCP,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,CAAC;EAEF,oBACEX,OAAA,CAACX,SAAS;IAACkC,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACvCzB,OAAA,CAACV,KAAK;MAACoC,SAAS,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACvCzB,OAAA,CAACT,UAAU;QAACgC,SAAS,EAAC,IAAI;QAACO,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,QAAQ;QAACC,YAAY;QAAAP,QAAA,EAAC;MAEpE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpC,OAAA;QAAMkB,QAAQ,EAAEF,MAAM,CAACqB,YAAa;QAAAZ,QAAA,gBAClCzB,OAAA,CAACH,gBAAgB;UACfyC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAExB,MAAM,CAACG,MAAM,CAAChB,KAAM;UAC3BsC,QAAQ,EAAEzB,MAAM,CAAC0B,YAAa;UAC9B9B,KAAK,EAAEI,MAAM,CAAC2B,OAAO,CAACxC,KAAK,IAAIyC,OAAO,CAAC5B,MAAM,CAAC6B,MAAM,CAAC1C,KAAK,CAAE;UAC5D2C,UAAU,EAAE9B,MAAM,CAAC2B,OAAO,CAACxC,KAAK,IAAIa,MAAM,CAAC6B,MAAM,CAAC1C;QAAM;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACFpC,OAAA,CAACH,gBAAgB;UACfyC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,UAAU;UAChBQ,IAAI,EAAC,UAAU;UACfP,KAAK,EAAExB,MAAM,CAACG,MAAM,CAACb,QAAS;UAC9BmC,QAAQ,EAAEzB,MAAM,CAAC0B,YAAa;UAC9B9B,KAAK,EAAEI,MAAM,CAAC2B,OAAO,CAACrC,QAAQ,IAAIsC,OAAO,CAAC5B,MAAM,CAAC6B,MAAM,CAACvC,QAAQ,CAAE;UAClEwC,UAAU,EAAE9B,MAAM,CAAC2B,OAAO,CAACrC,QAAQ,IAAIU,MAAM,CAAC6B,MAAM,CAACvC;QAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACFpC,OAAA,CAACR,MAAM;UACLuD,IAAI,EAAC,QAAQ;UACbC,SAAS;UACTlB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEoB,EAAE,EAAE;UAAE,CAAE;UACrBC,QAAQ,EAAExC,OAAQ;UAAAe,QAAA,EAEjBf,OAAO,gBAAGV,OAAA,CAACN,gBAAgB;YAACyD,IAAI,EAAE;UAAG;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACTpC,OAAA,CAACP,GAAG;UAACkC,EAAE,EAAE;YAAEyB,SAAS,EAAE;UAAS,CAAE;UAAA3B,QAAA,gBAC/BzB,OAAA,CAACf,IAAI;YAACsC,SAAS,EAAErC,UAAW;YAACmE,EAAE,EAAC,kBAAkB;YAACvB,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpC,OAAA,CAACP,GAAG;YAACkC,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eACjBzB,OAAA,CAACf,IAAI;cAACsC,SAAS,EAAErC,UAAW;cAACmE,EAAE,EAAC,SAAS;cAACvB,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE1D;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACRpC,OAAA,CAACL,QAAQ;MACP2D,IAAI,EAAExC,SAAU;MAChByC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,KAAK,CAAE;MAAAU,QAAA,eAEnCzB,OAAA,CAACJ,KAAK;QAAC6D,QAAQ,EAAC,OAAO;QAACD,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,KAAK,CAAE;QAAAU,QAAA,EACxDb;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAC5B,EAAA,CAlFID,KAAK;EAAA,QACQvB,WAAW,EAKbG,SAAS;AAAA;AAAAuE,EAAA,GANpBnD,KAAK;AAoFX,eAAeA,KAAK;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}