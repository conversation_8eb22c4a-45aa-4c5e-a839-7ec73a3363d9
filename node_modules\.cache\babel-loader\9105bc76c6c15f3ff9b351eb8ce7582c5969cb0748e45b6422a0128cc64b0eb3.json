{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\pages\\\\ForgotPassword.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Link as RouterLink } from 'react-router-dom';\nimport { useFormik } from 'formik';\nimport * as Yup from 'yup';\nimport { Container, Paper, Typography, Button, Link, Box, CircularProgress, Snackbar, Alert } from '@mui/material';\nimport TextFieldWrapper from '../components/TextFieldWrapper';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst validationSchema = Yup.object({\n  email: Yup.string().email('Invalid email address').required('Email is required')\n});\nconst ForgotPassword = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n  const [success, setSuccess] = useState(false);\n  const formik = useFormik({\n    initialValues: {\n      email: ''\n    },\n    validationSchema,\n    onSubmit: async values => {\n      setLoading(true);\n      try {\n        await authService.forgotPassword(values.email);\n        setSuccess(true);\n      } catch (err) {\n        setError(err.message || 'Failed to process request');\n        setShowError(true);\n      } finally {\n        setLoading(false);\n      }\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"xs\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4,\n        mt: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        component: \"h1\",\n        variant: \"h5\",\n        align: \"center\",\n        gutterBottom: true,\n        children: \"Forgot Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), !success ? /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: formik.handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          align: \"center\",\n          sx: {\n            mb: 3\n          },\n          children: \"Enter your email address and we'll send you a link to reset your password.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextFieldWrapper, {\n          name: \"email\",\n          label: \"Email Address\",\n          value: formik.values.email,\n          onChange: formik.handleChange,\n          error: formik.touched.email && Boolean(formik.errors.email),\n          helperText: formik.touched.email && formik.errors.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 26\n          }, this) : 'Send Reset Link'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            component: RouterLink,\n            to: \"/login\",\n            variant: \"body2\",\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"primary\",\n          gutterBottom: true,\n          children: \"Password reset link has been sent to your email.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          component: RouterLink,\n          to: \"/login\",\n          variant: \"contained\",\n          sx: {\n            mt: 2\n          },\n          children: \"Back to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: () => setShowError(false),\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        onClose: () => setShowError(false),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(ForgotPassword, \"zqq+ZCKDVGqfB4Fm1FS24NLG37s=\", false, function () {\n  return [useFormik];\n});\n_c = ForgotPassword;\nexport default ForgotPassword;\nvar _c;\n$RefreshReg$(_c, \"ForgotPassword\");", "map": {"version": 3, "names": ["useState", "Link", "RouterLink", "useFormik", "<PERSON><PERSON>", "Container", "Paper", "Typography", "<PERSON><PERSON>", "Box", "CircularProgress", "Snackbar", "<PERSON><PERSON>", "TextFieldWrapper", "authService", "jsxDEV", "_jsxDEV", "validationSchema", "object", "email", "string", "required", "ForgotPassword", "_s", "loading", "setLoading", "error", "setError", "showError", "setShowError", "success", "setSuccess", "formik", "initialValues", "onSubmit", "values", "forgotPassword", "err", "message", "component", "max<PERSON><PERSON><PERSON>", "children", "elevation", "sx", "p", "mt", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleSubmit", "color", "mb", "name", "label", "value", "onChange", "handleChange", "touched", "Boolean", "errors", "helperText", "type", "fullWidth", "disabled", "size", "textAlign", "to", "open", "autoHideDuration", "onClose", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/pages/ForgotPassword.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { Link as RouterLink } from 'react-router-dom';\r\nimport { useFormik } from 'formik';\r\nimport * as Yup from 'yup';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  Button,\r\n  Link,\r\n  Box,\r\n  CircularProgress,\r\n  Snackbar,\r\n  Alert\r\n} from '@mui/material';\r\nimport TextFieldWrapper from '../components/TextFieldWrapper';\r\nimport authService from '../api/authService';\r\n\r\nconst validationSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required')\r\n});\r\n\r\nconst ForgotPassword = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n  const [success, setSuccess] = useState(false);\r\n\r\n  const formik = useFormik({\r\n    initialValues: {\r\n      email: ''\r\n    },\r\n    validationSchema,\r\n    onSubmit: async (values) => {\r\n      setLoading(true);\r\n      try {\r\n        await authService.forgotPassword(values.email);\r\n        setSuccess(true);\r\n      } catch (err) {\r\n        setError(err.message || 'Failed to process request');\r\n        setShowError(true);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  });\r\n\r\n  return (\r\n    <Container component=\"main\" maxWidth=\"xs\">\r\n      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>\r\n        <Typography component=\"h1\" variant=\"h5\" align=\"center\" gutterBottom>\r\n          Forgot Password\r\n        </Typography>\r\n        {!success ? (\r\n          <form onSubmit={formik.handleSubmit}>\r\n            <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{ mb: 3 }}>\r\n              Enter your email address and we'll send you a link to reset your password.\r\n            </Typography>\r\n            <TextFieldWrapper\r\n              name=\"email\"\r\n              label=\"Email Address\"\r\n              value={formik.values.email}\r\n              onChange={formik.handleChange}\r\n              error={formik.touched.email && Boolean(formik.errors.email)}\r\n              helperText={formik.touched.email && formik.errors.email}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              fullWidth\r\n              variant=\"contained\"\r\n              sx={{ mt: 3, mb: 2 }}\r\n              disabled={loading}\r\n            >\r\n              {loading ? <CircularProgress size={24} /> : 'Send Reset Link'}\r\n            </Button>\r\n            <Box sx={{ textAlign: 'center' }}>\r\n              <Link component={RouterLink} to=\"/login\" variant=\"body2\">\r\n                Back to Login\r\n              </Link>\r\n            </Box>\r\n          </form>\r\n        ) : (\r\n          <Box sx={{ textAlign: 'center' }}>\r\n            <Typography variant=\"body1\" color=\"primary\" gutterBottom>\r\n              Password reset link has been sent to your email.\r\n            </Typography>\r\n            <Button\r\n              component={RouterLink}\r\n              to=\"/login\"\r\n              variant=\"contained\"\r\n              sx={{ mt: 2 }}\r\n            >\r\n              Back to Login\r\n            </Button>\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={() => setShowError(false)}\r\n      >\r\n        <Alert severity=\"error\" onClose={() => setShowError(false)}>\r\n          {error}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ForgotPassword; "], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AACrD,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNP,IAAI,EACJQ,GAAG,EACHC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,gBAAgB,GAAGb,GAAG,CAACc,MAAM,CAAC;EAClCC,KAAK,EAAEf,GAAG,CAACgB,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB;AACjC,CAAC,CAAC;AAEF,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMgC,MAAM,GAAG7B,SAAS,CAAC;IACvB8B,aAAa,EAAE;MACbd,KAAK,EAAE;IACT,CAAC;IACDF,gBAAgB;IAChBiB,QAAQ,EAAE,MAAOC,MAAM,IAAK;MAC1BV,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMX,WAAW,CAACsB,cAAc,CAACD,MAAM,CAAChB,KAAK,CAAC;QAC9CY,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZV,QAAQ,CAACU,GAAG,CAACC,OAAO,IAAI,2BAA2B,CAAC;QACpDT,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,CAAC;EAEF,oBACET,OAAA,CAACX,SAAS;IAACkC,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACvCzB,OAAA,CAACV,KAAK;MAACoC,SAAS,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACvCzB,OAAA,CAACT,UAAU;QAACgC,SAAS,EAAC,IAAI;QAACO,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,QAAQ;QAACC,YAAY;QAAAP,QAAA,EAAC;MAEpE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ,CAACtB,OAAO,gBACPd,OAAA;QAAMkB,QAAQ,EAAEF,MAAM,CAACqB,YAAa;QAAAZ,QAAA,gBAClCzB,OAAA,CAACT,UAAU;UAACuC,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,eAAe;UAACP,KAAK,EAAC,QAAQ;UAACJ,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEhF;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA,CAACH,gBAAgB;UACf2C,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE1B,MAAM,CAACG,MAAM,CAAChB,KAAM;UAC3BwC,QAAQ,EAAE3B,MAAM,CAAC4B,YAAa;UAC9BlC,KAAK,EAAEM,MAAM,CAAC6B,OAAO,CAAC1C,KAAK,IAAI2C,OAAO,CAAC9B,MAAM,CAAC+B,MAAM,CAAC5C,KAAK,CAAE;UAC5D6C,UAAU,EAAEhC,MAAM,CAAC6B,OAAO,CAAC1C,KAAK,IAAIa,MAAM,CAAC+B,MAAM,CAAC5C;QAAM;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACFpC,OAAA,CAACR,MAAM;UACLyD,IAAI,EAAC,QAAQ;UACbC,SAAS;UACTpB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEU,EAAE,EAAE;UAAE,CAAE;UACrBY,QAAQ,EAAE3C,OAAQ;UAAAiB,QAAA,EAEjBjB,OAAO,gBAAGR,OAAA,CAACN,gBAAgB;YAAC0D,IAAI,EAAE;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAiB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACTpC,OAAA,CAACP,GAAG;UAACkC,EAAE,EAAE;YAAE0B,SAAS,EAAE;UAAS,CAAE;UAAA5B,QAAA,eAC/BzB,OAAA,CAACf,IAAI;YAACsC,SAAS,EAAErC,UAAW;YAACoE,EAAE,EAAC,QAAQ;YAACxB,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAEzD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEPpC,OAAA,CAACP,GAAG;QAACkC,EAAE,EAAE;UAAE0B,SAAS,EAAE;QAAS,CAAE;QAAA5B,QAAA,gBAC/BzB,OAAA,CAACT,UAAU;UAACuC,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,SAAS;UAACN,YAAY;UAAAP,QAAA,EAAC;QAEzD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA,CAACR,MAAM;UACL+B,SAAS,EAAErC,UAAW;UACtBoE,EAAE,EAAC,QAAQ;UACXxB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EACf;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACRpC,OAAA,CAACL,QAAQ;MACP4D,IAAI,EAAE3C,SAAU;MAChB4C,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,KAAK,CAAE;MAAAY,QAAA,eAEnCzB,OAAA,CAACJ,KAAK;QAAC8D,QAAQ,EAAC,OAAO;QAACD,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,KAAK,CAAE;QAAAY,QAAA,EACxDf;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAC7B,EAAA,CAtFID,cAAc;EAAA,QAMHnB,SAAS;AAAA;AAAAwE,EAAA,GANpBrD,cAAc;AAwFpB,eAAeA,cAAc;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}