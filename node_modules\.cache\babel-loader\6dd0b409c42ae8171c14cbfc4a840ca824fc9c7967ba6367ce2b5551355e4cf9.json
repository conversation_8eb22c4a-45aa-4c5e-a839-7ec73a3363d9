{"ast": null, "code": "'use client';\n\nexport { default } from './PaginationItem';\nexport { default as paginationItemClasses } from './paginationItemClasses';\nexport * from './paginationItemClasses';", "map": {"version": 3, "names": ["default", "paginationItemClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/PaginationItem/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './PaginationItem';\nexport { default as paginationItemClasses } from './paginationItemClasses';\nexport * from './paginationItemClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}