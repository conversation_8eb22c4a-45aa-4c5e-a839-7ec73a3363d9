{"ast": null, "code": "'use client';\n\nexport { default } from './Stack';\nexport { default as stackClasses } from './stackClasses';", "map": {"version": 3, "names": ["default", "stackClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/Stack/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Stack';\nexport { default as stackClasses } from './stackClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}