{"ast": null, "code": "'use client';\n\nexport { default } from './TextField';\nexport { default as textFieldClasses } from './textFieldClasses';\nexport * from './textFieldClasses';", "map": {"version": 3, "names": ["default", "textFieldClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/TextField/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TextField';\nexport { default as textFieldClasses } from './textFieldClasses';\nexport * from './textFieldClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}