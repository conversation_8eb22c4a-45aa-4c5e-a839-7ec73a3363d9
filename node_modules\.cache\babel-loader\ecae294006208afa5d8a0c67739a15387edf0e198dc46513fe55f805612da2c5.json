{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useTheme as useThemeSystem } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useTheme() {\n  const theme = useThemeSystem(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[THEME_ID] || theme;\n}", "map": {"version": 3, "names": ["React", "useTheme", "useThemeSystem", "defaultTheme", "THEME_ID", "theme", "process", "env", "NODE_ENV", "useDebugValue"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/styles/useTheme.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useTheme as useThemeSystem } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useTheme() {\n  const theme = useThemeSystem(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[THEME_ID] || theme;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,IAAIC,cAAc,QAAQ,aAAa;AACxD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,eAAe,SAASH,QAAQA,CAAA,EAAG;EACjC,MAAMI,KAAK,GAAGH,cAAc,CAACC,YAAY,CAAC;EAC1C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAR,KAAK,CAACS,aAAa,CAACJ,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK,CAACD,QAAQ,CAAC,IAAIC,KAAK;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}