{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAppBarUtilityClass(slot) {\n  return generateUtilityClass('MuiAppBar', slot);\n}\nconst appBarClasses = generateUtilityClasses('MuiAppBar', ['root', 'positionFixed', 'positionAbsolute', 'positionSticky', 'positionStatic', 'positionRelative', 'colorDefault', 'colorPrimary', 'colorSecondary', 'colorInherit', 'colorTransparent', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning']);\nexport default appBarClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getAppBarUtilityClass", "slot", "appBarClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/AppBar/appBarClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAppBarUtilityClass(slot) {\n  return generateUtilityClass('MuiAppBar', slot);\n}\nconst appBarClasses = generateUtilityClasses('MuiAppBar', ['root', 'positionFixed', 'positionAbsolute', 'positionSticky', 'positionStatic', 'positionRelative', 'colorDefault', 'colorPrimary', 'colorSecondary', 'colorInherit', 'colorTransparent', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning']);\nexport default appBarClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;AACjT,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}