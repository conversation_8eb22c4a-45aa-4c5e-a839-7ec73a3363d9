{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"aria-describedby\", \"autoComplete\", \"autoFocus\", \"className\", \"color\", \"components\", \"componentsProps\", \"defaultValue\", \"disabled\", \"disableInjectingGlobalStyles\", \"endAdornment\", \"error\", \"fullWidth\", \"id\", \"inputComponent\", \"inputProps\", \"inputRef\", \"margin\", \"maxRows\", \"minRows\", \"multiline\", \"name\", \"onBlur\", \"onChange\", \"onClick\", \"onFocus\", \"onKeyDown\", \"onKeyUp\", \"placeholder\", \"readOnly\", \"renderSuffix\", \"rows\", \"size\", \"slotProps\", \"slots\", \"startAdornment\", \"type\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport TextareaAutosize from '../TextareaAutosize';\nimport formControlState from '../FormControl/formControlState';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport useForkRef from '../utils/useForkRef';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport GlobalStyles from '../GlobalStyles';\nimport { isFilled } from './utils';\nimport inputBaseClasses, { getInputBaseUtilityClass } from './inputBaseClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const rootOverridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.formControl && styles.formControl, ownerState.startAdornment && styles.adornedStart, ownerState.endAdornment && styles.adornedEnd, ownerState.error && styles.error, ownerState.size === 'small' && styles.sizeSmall, ownerState.multiline && styles.multiline, ownerState.color && styles[`color${capitalize(ownerState.color)}`], ownerState.fullWidth && styles.fullWidth, ownerState.hiddenLabel && styles.hiddenLabel];\n};\nexport const inputOverridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.input, ownerState.size === 'small' && styles.inputSizeSmall, ownerState.multiline && styles.inputMultiline, ownerState.type === 'search' && styles.inputTypeSearch, ownerState.startAdornment && styles.inputAdornedStart, ownerState.endAdornment && styles.inputAdornedEnd, ownerState.hiddenLabel && styles.inputHiddenLabel];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    disabled,\n    error,\n    endAdornment,\n    focused,\n    formControl,\n    fullWidth,\n    hiddenLabel,\n    multiline,\n    readOnly,\n    size,\n    startAdornment,\n    type\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', fullWidth && 'fullWidth', focused && 'focused', formControl && 'formControl', size && size !== 'medium' && `size${capitalize(size)}`, multiline && 'multiline', startAdornment && 'adornedStart', endAdornment && 'adornedEnd', hiddenLabel && 'hiddenLabel', readOnly && 'readOnly'],\n    input: ['input', disabled && 'disabled', type === 'search' && 'inputTypeSearch', multiline && 'inputMultiline', size === 'small' && 'inputSizeSmall', hiddenLabel && 'inputHiddenLabel', startAdornment && 'inputAdornedStart', endAdornment && 'inputAdornedEnd', readOnly && 'readOnly']\n  };\n  return composeClasses(slots, getInputBaseUtilityClass, classes);\n};\nexport const InputBaseRoot = styled('div', {\n  name: 'MuiInputBase',\n  slot: 'Root',\n  overridesResolver: rootOverridesResolver\n})(({\n  theme,\n  ownerState\n}) => _extends({}, theme.typography.body1, {\n  color: (theme.vars || theme).palette.text.primary,\n  lineHeight: '1.4375em',\n  // 23px\n  boxSizing: 'border-box',\n  // Prevent padding issue with fullWidth.\n  position: 'relative',\n  cursor: 'text',\n  display: 'inline-flex',\n  alignItems: 'center',\n  [`&.${inputBaseClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled,\n    cursor: 'default'\n  }\n}, ownerState.multiline && _extends({\n  padding: '4px 0 5px'\n}, ownerState.size === 'small' && {\n  paddingTop: 1\n}), ownerState.fullWidth && {\n  width: '100%'\n}));\nexport const InputBaseComponent = styled('input', {\n  name: 'MuiInputBase',\n  slot: 'Input',\n  overridesResolver: inputOverridesResolver\n})(({\n  theme,\n  ownerState\n}) => {\n  const light = theme.palette.mode === 'light';\n  const placeholder = _extends({\n    color: 'currentColor'\n  }, theme.vars ? {\n    opacity: theme.vars.opacity.inputPlaceholder\n  } : {\n    opacity: light ? 0.42 : 0.5\n  }, {\n    transition: theme.transitions.create('opacity', {\n      duration: theme.transitions.duration.shorter\n    })\n  });\n  const placeholderHidden = {\n    opacity: '0 !important'\n  };\n  const placeholderVisible = theme.vars ? {\n    opacity: theme.vars.opacity.inputPlaceholder\n  } : {\n    opacity: light ? 0.42 : 0.5\n  };\n  return _extends({\n    font: 'inherit',\n    letterSpacing: 'inherit',\n    color: 'currentColor',\n    padding: '4px 0 5px',\n    border: 0,\n    boxSizing: 'content-box',\n    background: 'none',\n    height: '1.4375em',\n    // Reset 23pxthe native input line-height\n    margin: 0,\n    // Reset for Safari\n    WebkitTapHighlightColor: 'transparent',\n    display: 'block',\n    // Make the flex item shrink with Firefox\n    minWidth: 0,\n    width: '100%',\n    // Fix IE11 width issue\n    animationName: 'mui-auto-fill-cancel',\n    animationDuration: '10ms',\n    '&::-webkit-input-placeholder': placeholder,\n    '&::-moz-placeholder': placeholder,\n    // Firefox 19+\n    '&:-ms-input-placeholder': placeholder,\n    // IE11\n    '&::-ms-input-placeholder': placeholder,\n    // Edge\n    '&:focus': {\n      outline: 0\n    },\n    // Reset Firefox invalid required input style\n    '&:invalid': {\n      boxShadow: 'none'\n    },\n    '&::-webkit-search-decoration': {\n      // Remove the padding when type=search.\n      WebkitAppearance: 'none'\n    },\n    // Show and hide the placeholder logic\n    [`label[data-shrink=false] + .${inputBaseClasses.formControl} &`]: {\n      '&::-webkit-input-placeholder': placeholderHidden,\n      '&::-moz-placeholder': placeholderHidden,\n      // Firefox 19+\n      '&:-ms-input-placeholder': placeholderHidden,\n      // IE11\n      '&::-ms-input-placeholder': placeholderHidden,\n      // Edge\n      '&:focus::-webkit-input-placeholder': placeholderVisible,\n      '&:focus::-moz-placeholder': placeholderVisible,\n      // Firefox 19+\n      '&:focus:-ms-input-placeholder': placeholderVisible,\n      // IE11\n      '&:focus::-ms-input-placeholder': placeholderVisible // Edge\n    },\n    [`&.${inputBaseClasses.disabled}`]: {\n      opacity: 1,\n      // Reset iOS opacity\n      WebkitTextFillColor: (theme.vars || theme).palette.text.disabled // Fix opacity Safari bug\n    },\n    '&:-webkit-autofill': {\n      animationDuration: '5000s',\n      animationName: 'mui-auto-fill'\n    }\n  }, ownerState.size === 'small' && {\n    paddingTop: 1\n  }, ownerState.multiline && {\n    height: 'auto',\n    resize: 'none',\n    padding: 0,\n    paddingTop: 0\n  }, ownerState.type === 'search' && {\n    // Improve type search style.\n    MozAppearance: 'textfield'\n  });\n});\nconst inputGlobalStyles = /*#__PURE__*/_jsx(GlobalStyles, {\n  styles: {\n    '@keyframes mui-auto-fill': {\n      from: {\n        display: 'block'\n      }\n    },\n    '@keyframes mui-auto-fill-cancel': {\n      from: {\n        display: 'block'\n      }\n    }\n  }\n});\n\n/**\n * `InputBase` contains as few styles as possible.\n * It aims to be a simple building block for creating an input.\n * It contains a load of style reset and some state logic.\n */\nconst InputBase = /*#__PURE__*/React.forwardRef(function InputBase(inProps, ref) {\n  var _slotProps$input;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiInputBase'\n  });\n  const {\n      'aria-describedby': ariaDescribedby,\n      autoComplete,\n      autoFocus,\n      className,\n      components = {},\n      componentsProps = {},\n      defaultValue,\n      disabled,\n      disableInjectingGlobalStyles,\n      endAdornment,\n      fullWidth = false,\n      id,\n      inputComponent = 'input',\n      inputProps: inputPropsProp = {},\n      inputRef: inputRefProp,\n      maxRows,\n      minRows,\n      multiline = false,\n      name,\n      onBlur,\n      onChange,\n      onClick,\n      onFocus,\n      onKeyDown,\n      onKeyUp,\n      placeholder,\n      readOnly,\n      renderSuffix,\n      rows,\n      slotProps = {},\n      slots = {},\n      startAdornment,\n      type = 'text',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const value = inputPropsProp.value != null ? inputPropsProp.value : valueProp;\n  const {\n    current: isControlled\n  } = React.useRef(value != null);\n  const inputRef = React.useRef();\n  const handleInputRefWarning = React.useCallback(instance => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (instance && instance.nodeName !== 'INPUT' && !instance.focus) {\n        console.error(['MUI: You have provided a `inputComponent` to the input component', 'that does not correctly handle the `ref` prop.', 'Make sure the `ref` prop is called with a HTMLInputElement.'].join('\\n'));\n      }\n    }\n  }, []);\n  const handleInputRef = useForkRef(inputRef, inputRefProp, inputPropsProp.ref, handleInputRefWarning);\n  const [focused, setFocused] = React.useState(false);\n  const muiFormControl = useFormControl();\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (muiFormControl) {\n        return muiFormControl.registerEffect();\n      }\n      return undefined;\n    }, [muiFormControl]);\n  }\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'disabled', 'error', 'hiddenLabel', 'size', 'required', 'filled']\n  });\n  fcs.focused = muiFormControl ? muiFormControl.focused : focused;\n\n  // The blur won't fire when the disabled state is set on a focused input.\n  // We need to book keep the focused state manually.\n  React.useEffect(() => {\n    if (!muiFormControl && disabled && focused) {\n      setFocused(false);\n      if (onBlur) {\n        onBlur();\n      }\n    }\n  }, [muiFormControl, disabled, focused, onBlur]);\n  const onFilled = muiFormControl && muiFormControl.onFilled;\n  const onEmpty = muiFormControl && muiFormControl.onEmpty;\n  const checkDirty = React.useCallback(obj => {\n    if (isFilled(obj)) {\n      if (onFilled) {\n        onFilled();\n      }\n    } else if (onEmpty) {\n      onEmpty();\n    }\n  }, [onFilled, onEmpty]);\n  useEnhancedEffect(() => {\n    if (isControlled) {\n      checkDirty({\n        value\n      });\n    }\n  }, [value, checkDirty, isControlled]);\n  const handleFocus = event => {\n    // Fix a bug with IE11 where the focus/blur events are triggered\n    // while the component is disabled.\n    if (fcs.disabled) {\n      event.stopPropagation();\n      return;\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n    if (inputPropsProp.onFocus) {\n      inputPropsProp.onFocus(event);\n    }\n    if (muiFormControl && muiFormControl.onFocus) {\n      muiFormControl.onFocus(event);\n    } else {\n      setFocused(true);\n    }\n  };\n  const handleBlur = event => {\n    if (onBlur) {\n      onBlur(event);\n    }\n    if (inputPropsProp.onBlur) {\n      inputPropsProp.onBlur(event);\n    }\n    if (muiFormControl && muiFormControl.onBlur) {\n      muiFormControl.onBlur(event);\n    } else {\n      setFocused(false);\n    }\n  };\n  const handleChange = (event, ...args) => {\n    if (!isControlled) {\n      const element = event.target || inputRef.current;\n      if (element == null) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Expected valid input target. Did you use a custom \\`inputComponent\\` and forget to forward refs? See https://mui.com/r/input-component-ref-interface for more info.` : _formatMuiErrorMessage(1));\n      }\n      checkDirty({\n        value: element.value\n      });\n    }\n    if (inputPropsProp.onChange) {\n      inputPropsProp.onChange(event, ...args);\n    }\n\n    // Perform in the willUpdate\n    if (onChange) {\n      onChange(event, ...args);\n    }\n  };\n\n  // Check the input state on mount, in case it was filled by the user\n  // or auto filled by the browser before the hydration (for SSR).\n  React.useEffect(() => {\n    checkDirty(inputRef.current);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const handleClick = event => {\n    if (inputRef.current && event.currentTarget === event.target) {\n      inputRef.current.focus();\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  let InputComponent = inputComponent;\n  let inputProps = inputPropsProp;\n  if (multiline && InputComponent === 'input') {\n    if (rows) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (minRows || maxRows) {\n          console.warn('MUI: You can not use the `minRows` or `maxRows` props when the input `rows` prop is set.');\n        }\n      }\n      inputProps = _extends({\n        type: undefined,\n        minRows: rows,\n        maxRows: rows\n      }, inputProps);\n    } else {\n      inputProps = _extends({\n        type: undefined,\n        maxRows,\n        minRows\n      }, inputProps);\n    }\n    InputComponent = TextareaAutosize;\n  }\n  const handleAutoFill = event => {\n    // Provide a fake value as Chrome might not let you access it for security reasons.\n    checkDirty(event.animationName === 'mui-auto-fill-cancel' ? inputRef.current : {\n      value: 'x'\n    });\n  };\n  React.useEffect(() => {\n    if (muiFormControl) {\n      muiFormControl.setAdornedStart(Boolean(startAdornment));\n    }\n  }, [muiFormControl, startAdornment]);\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    disabled: fcs.disabled,\n    endAdornment,\n    error: fcs.error,\n    focused: fcs.focused,\n    formControl: muiFormControl,\n    fullWidth,\n    hiddenLabel: fcs.hiddenLabel,\n    multiline,\n    size: fcs.size,\n    startAdornment,\n    type\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = slots.root || components.Root || InputBaseRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const Input = slots.input || components.Input || InputBaseComponent;\n  inputProps = _extends({}, inputProps, (_slotProps$input = slotProps.input) != null ? _slotProps$input : componentsProps.input);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [!disableInjectingGlobalStyles && inputGlobalStyles, /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, !isHostComponent(Root) && {\n      ownerState: _extends({}, ownerState, rootProps.ownerState)\n    }, {\n      ref: ref,\n      onClick: handleClick\n    }, other, {\n      className: clsx(classes.root, rootProps.className, className, readOnly && 'MuiInputBase-readOnly'),\n      children: [startAdornment, /*#__PURE__*/_jsx(FormControlContext.Provider, {\n        value: null,\n        children: /*#__PURE__*/_jsx(Input, _extends({\n          ownerState: ownerState,\n          \"aria-invalid\": fcs.error,\n          \"aria-describedby\": ariaDescribedby,\n          autoComplete: autoComplete,\n          autoFocus: autoFocus,\n          defaultValue: defaultValue,\n          disabled: fcs.disabled,\n          id: id,\n          onAnimationStart: handleAutoFill,\n          name: name,\n          placeholder: placeholder,\n          readOnly: readOnly,\n          required: fcs.required,\n          rows: rows,\n          value: value,\n          onKeyDown: onKeyDown,\n          onKeyUp: onKeyUp,\n          type: type\n        }, inputProps, !isHostComponent(Input) && {\n          as: InputComponent,\n          ownerState: _extends({}, ownerState, inputProps.ownerState)\n        }, {\n          ref: handleInputRef,\n          className: clsx(classes.input, inputProps.className, readOnly && 'MuiInputBase-readOnly'),\n          onBlur: handleBlur,\n          onChange: handleChange,\n          onFocus: handleFocus\n        }))\n      }), endAdornment, renderSuffix ? renderSuffix(_extends({}, fcs, {\n        startAdornment\n      })) : null]\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? InputBase.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * This prop helps users to fill forms faster, especially on mobile devices.\n   * The name can be confusing, as it's more like an autofill.\n   * You can learn more about it [following the specification](https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#autofill).\n   */\n  autoComplete: PropTypes.string,\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * The prop defaults to the value (`'primary'`) inherited from the parent FormControl component.\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Input: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    input: PropTypes.object,\n    root: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the component is disabled.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, GlobalStyles for the auto-fill keyframes will not be injected/removed on mount/unmount. Make sure to inject them at the top of your application.\n   * This option is intended to help with boosting the initial rendering performance if you are loading a big amount of Input components at once.\n   * @default false\n   */\n  disableInjectingGlobalStyles: PropTypes.bool,\n  /**\n   * End `InputAdornment` for this component.\n   */\n  endAdornment: PropTypes.node,\n  /**\n   * If `true`, the `input` will indicate an error.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the `input` will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * The component used for the `input` element.\n   * Either a string to use a HTML element or a component.\n   * @default 'input'\n   */\n  inputComponent: elementTypeAcceptingRef,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   * @default {}\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   * The prop defaults to the value (`'none'`) inherited from the parent FormControl component.\n   */\n  margin: PropTypes.oneOf(['dense', 'none']),\n  /**\n   * Maximum number of rows to display when multiline option is set to true.\n   */\n  maxRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Minimum number of rows to display when multiline option is set to true.\n   */\n  minRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * If `true`, a [TextareaAutosize](/material-ui/react-textarea-autosize/) element is rendered.\n   * @default false\n   */\n  multiline: PropTypes.bool,\n  /**\n   * Name attribute of the `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the `input` is blurred.\n   *\n   * Notice that the first argument (event) might be undefined.\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the value is changed.\n   *\n   * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the `input` doesn't satisfy its constraints.\n   */\n  onInvalid: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The short hint displayed in the `input` before the user enters a value.\n   */\n  placeholder: PropTypes.string,\n  /**\n   * It prevents the user from changing the value of the field\n   * (not from interacting with the field).\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  renderSuffix: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  required: PropTypes.bool,\n  /**\n   * Number of rows to display when multiline option is set to true.\n   */\n  rows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The size of the component.\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.object,\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * Start `InputAdornment` for this component.\n   */\n  startAdornment: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Type of the `input` element. It should be [a valid HTML5 input type](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types).\n   * @default 'text'\n   */\n  type: PropTypes.string,\n  /**\n   * The value of the `input` element, required for a controlled component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default InputBase;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_formatMuiErrorMessage", "_excluded", "React", "PropTypes", "clsx", "elementTypeAcceptingRef", "refType", "composeClasses", "isHostComponent", "TextareaAutosize", "formControlState", "FormControlContext", "useFormControl", "styled", "useDefaultProps", "capitalize", "useForkRef", "useEnhancedEffect", "GlobalStyles", "isFilled", "inputBaseClasses", "getInputBaseUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "rootOverridesResolver", "props", "styles", "ownerState", "root", "formControl", "startAdornment", "adornedStart", "endAdornment", "adornedEnd", "error", "size", "sizeSmall", "multiline", "color", "fullWidth", "hidden<PERSON>abel", "inputOverridesResolver", "input", "inputSizeSmall", "inputMultiline", "type", "inputTypeSearch", "inputAdornedStart", "inputAdornedEnd", "inputHiddenLabel", "useUtilityClasses", "classes", "disabled", "focused", "readOnly", "slots", "InputBaseRoot", "name", "slot", "overridesResolver", "theme", "typography", "body1", "vars", "palette", "text", "primary", "lineHeight", "boxSizing", "position", "cursor", "display", "alignItems", "padding", "paddingTop", "width", "InputBaseComponent", "light", "mode", "placeholder", "opacity", "inputPlaceholder", "transition", "transitions", "create", "duration", "shorter", "placeholder<PERSON><PERSON>den", "placeholderVisible", "font", "letterSpacing", "border", "background", "height", "margin", "WebkitTapHighlightColor", "min<PERSON><PERSON><PERSON>", "animationName", "animationDuration", "outline", "boxShadow", "WebkitAppearance", "WebkitTextFillColor", "resize", "MozAppearance", "inputGlobalStyles", "from", "InputBase", "forwardRef", "inProps", "ref", "_slotProps$input", "aria<PERSON><PERSON><PERSON><PERSON>", "autoComplete", "autoFocus", "className", "components", "componentsProps", "defaultValue", "disableInjectingGlobalStyles", "id", "inputComponent", "inputProps", "inputPropsProp", "inputRef", "inputRefProp", "maxRows", "minRows", "onBlur", "onChange", "onClick", "onFocus", "onKeyDown", "onKeyUp", "renderSuffix", "rows", "slotProps", "value", "valueProp", "other", "current", "isControlled", "useRef", "handleInputRefWarning", "useCallback", "instance", "process", "env", "NODE_ENV", "nodeName", "focus", "console", "join", "handleInputRef", "setFocused", "useState", "muiFormControl", "useEffect", "registerEffect", "undefined", "fcs", "states", "onFilled", "onEmpty", "checkDirty", "obj", "handleFocus", "event", "stopPropagation", "handleBlur", "handleChange", "args", "element", "target", "Error", "handleClick", "currentTarget", "InputComponent", "warn", "handleAutoFill", "setAdornedStart", "Boolean", "Root", "rootProps", "Input", "Fragment", "children", "Provider", "onAnimationStart", "required", "as", "propTypes", "string", "bool", "object", "oneOfType", "oneOf", "shape", "elementType", "any", "node", "number", "func", "onInvalid", "sx", "arrayOf"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/InputBase/InputBase.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"aria-describedby\", \"autoComplete\", \"autoFocus\", \"className\", \"color\", \"components\", \"componentsProps\", \"defaultValue\", \"disabled\", \"disableInjectingGlobalStyles\", \"endAdornment\", \"error\", \"fullWidth\", \"id\", \"inputComponent\", \"inputProps\", \"inputRef\", \"margin\", \"maxRows\", \"minRows\", \"multiline\", \"name\", \"onBlur\", \"onChange\", \"onClick\", \"onFocus\", \"onKeyDown\", \"onKeyUp\", \"placeholder\", \"readOnly\", \"renderSuffix\", \"rows\", \"size\", \"slotProps\", \"slots\", \"startAdornment\", \"type\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport TextareaAutosize from '../TextareaAutosize';\nimport formControlState from '../FormControl/formControlState';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport useForkRef from '../utils/useForkRef';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport GlobalStyles from '../GlobalStyles';\nimport { isFilled } from './utils';\nimport inputBaseClasses, { getInputBaseUtilityClass } from './inputBaseClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const rootOverridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.formControl && styles.formControl, ownerState.startAdornment && styles.adornedStart, ownerState.endAdornment && styles.adornedEnd, ownerState.error && styles.error, ownerState.size === 'small' && styles.sizeSmall, ownerState.multiline && styles.multiline, ownerState.color && styles[`color${capitalize(ownerState.color)}`], ownerState.fullWidth && styles.fullWidth, ownerState.hiddenLabel && styles.hiddenLabel];\n};\nexport const inputOverridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.input, ownerState.size === 'small' && styles.inputSizeSmall, ownerState.multiline && styles.inputMultiline, ownerState.type === 'search' && styles.inputTypeSearch, ownerState.startAdornment && styles.inputAdornedStart, ownerState.endAdornment && styles.inputAdornedEnd, ownerState.hiddenLabel && styles.inputHiddenLabel];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    disabled,\n    error,\n    endAdornment,\n    focused,\n    formControl,\n    fullWidth,\n    hiddenLabel,\n    multiline,\n    readOnly,\n    size,\n    startAdornment,\n    type\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', fullWidth && 'fullWidth', focused && 'focused', formControl && 'formControl', size && size !== 'medium' && `size${capitalize(size)}`, multiline && 'multiline', startAdornment && 'adornedStart', endAdornment && 'adornedEnd', hiddenLabel && 'hiddenLabel', readOnly && 'readOnly'],\n    input: ['input', disabled && 'disabled', type === 'search' && 'inputTypeSearch', multiline && 'inputMultiline', size === 'small' && 'inputSizeSmall', hiddenLabel && 'inputHiddenLabel', startAdornment && 'inputAdornedStart', endAdornment && 'inputAdornedEnd', readOnly && 'readOnly']\n  };\n  return composeClasses(slots, getInputBaseUtilityClass, classes);\n};\nexport const InputBaseRoot = styled('div', {\n  name: 'MuiInputBase',\n  slot: 'Root',\n  overridesResolver: rootOverridesResolver\n})(({\n  theme,\n  ownerState\n}) => _extends({}, theme.typography.body1, {\n  color: (theme.vars || theme).palette.text.primary,\n  lineHeight: '1.4375em',\n  // 23px\n  boxSizing: 'border-box',\n  // Prevent padding issue with fullWidth.\n  position: 'relative',\n  cursor: 'text',\n  display: 'inline-flex',\n  alignItems: 'center',\n  [`&.${inputBaseClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled,\n    cursor: 'default'\n  }\n}, ownerState.multiline && _extends({\n  padding: '4px 0 5px'\n}, ownerState.size === 'small' && {\n  paddingTop: 1\n}), ownerState.fullWidth && {\n  width: '100%'\n}));\nexport const InputBaseComponent = styled('input', {\n  name: 'MuiInputBase',\n  slot: 'Input',\n  overridesResolver: inputOverridesResolver\n})(({\n  theme,\n  ownerState\n}) => {\n  const light = theme.palette.mode === 'light';\n  const placeholder = _extends({\n    color: 'currentColor'\n  }, theme.vars ? {\n    opacity: theme.vars.opacity.inputPlaceholder\n  } : {\n    opacity: light ? 0.42 : 0.5\n  }, {\n    transition: theme.transitions.create('opacity', {\n      duration: theme.transitions.duration.shorter\n    })\n  });\n  const placeholderHidden = {\n    opacity: '0 !important'\n  };\n  const placeholderVisible = theme.vars ? {\n    opacity: theme.vars.opacity.inputPlaceholder\n  } : {\n    opacity: light ? 0.42 : 0.5\n  };\n  return _extends({\n    font: 'inherit',\n    letterSpacing: 'inherit',\n    color: 'currentColor',\n    padding: '4px 0 5px',\n    border: 0,\n    boxSizing: 'content-box',\n    background: 'none',\n    height: '1.4375em',\n    // Reset 23pxthe native input line-height\n    margin: 0,\n    // Reset for Safari\n    WebkitTapHighlightColor: 'transparent',\n    display: 'block',\n    // Make the flex item shrink with Firefox\n    minWidth: 0,\n    width: '100%',\n    // Fix IE11 width issue\n    animationName: 'mui-auto-fill-cancel',\n    animationDuration: '10ms',\n    '&::-webkit-input-placeholder': placeholder,\n    '&::-moz-placeholder': placeholder,\n    // Firefox 19+\n    '&:-ms-input-placeholder': placeholder,\n    // IE11\n    '&::-ms-input-placeholder': placeholder,\n    // Edge\n    '&:focus': {\n      outline: 0\n    },\n    // Reset Firefox invalid required input style\n    '&:invalid': {\n      boxShadow: 'none'\n    },\n    '&::-webkit-search-decoration': {\n      // Remove the padding when type=search.\n      WebkitAppearance: 'none'\n    },\n    // Show and hide the placeholder logic\n    [`label[data-shrink=false] + .${inputBaseClasses.formControl} &`]: {\n      '&::-webkit-input-placeholder': placeholderHidden,\n      '&::-moz-placeholder': placeholderHidden,\n      // Firefox 19+\n      '&:-ms-input-placeholder': placeholderHidden,\n      // IE11\n      '&::-ms-input-placeholder': placeholderHidden,\n      // Edge\n      '&:focus::-webkit-input-placeholder': placeholderVisible,\n      '&:focus::-moz-placeholder': placeholderVisible,\n      // Firefox 19+\n      '&:focus:-ms-input-placeholder': placeholderVisible,\n      // IE11\n      '&:focus::-ms-input-placeholder': placeholderVisible // Edge\n    },\n    [`&.${inputBaseClasses.disabled}`]: {\n      opacity: 1,\n      // Reset iOS opacity\n      WebkitTextFillColor: (theme.vars || theme).palette.text.disabled // Fix opacity Safari bug\n    },\n    '&:-webkit-autofill': {\n      animationDuration: '5000s',\n      animationName: 'mui-auto-fill'\n    }\n  }, ownerState.size === 'small' && {\n    paddingTop: 1\n  }, ownerState.multiline && {\n    height: 'auto',\n    resize: 'none',\n    padding: 0,\n    paddingTop: 0\n  }, ownerState.type === 'search' && {\n    // Improve type search style.\n    MozAppearance: 'textfield'\n  });\n});\nconst inputGlobalStyles = /*#__PURE__*/_jsx(GlobalStyles, {\n  styles: {\n    '@keyframes mui-auto-fill': {\n      from: {\n        display: 'block'\n      }\n    },\n    '@keyframes mui-auto-fill-cancel': {\n      from: {\n        display: 'block'\n      }\n    }\n  }\n});\n\n/**\n * `InputBase` contains as few styles as possible.\n * It aims to be a simple building block for creating an input.\n * It contains a load of style reset and some state logic.\n */\nconst InputBase = /*#__PURE__*/React.forwardRef(function InputBase(inProps, ref) {\n  var _slotProps$input;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiInputBase'\n  });\n  const {\n      'aria-describedby': ariaDescribedby,\n      autoComplete,\n      autoFocus,\n      className,\n      components = {},\n      componentsProps = {},\n      defaultValue,\n      disabled,\n      disableInjectingGlobalStyles,\n      endAdornment,\n      fullWidth = false,\n      id,\n      inputComponent = 'input',\n      inputProps: inputPropsProp = {},\n      inputRef: inputRefProp,\n      maxRows,\n      minRows,\n      multiline = false,\n      name,\n      onBlur,\n      onChange,\n      onClick,\n      onFocus,\n      onKeyDown,\n      onKeyUp,\n      placeholder,\n      readOnly,\n      renderSuffix,\n      rows,\n      slotProps = {},\n      slots = {},\n      startAdornment,\n      type = 'text',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const value = inputPropsProp.value != null ? inputPropsProp.value : valueProp;\n  const {\n    current: isControlled\n  } = React.useRef(value != null);\n  const inputRef = React.useRef();\n  const handleInputRefWarning = React.useCallback(instance => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (instance && instance.nodeName !== 'INPUT' && !instance.focus) {\n        console.error(['MUI: You have provided a `inputComponent` to the input component', 'that does not correctly handle the `ref` prop.', 'Make sure the `ref` prop is called with a HTMLInputElement.'].join('\\n'));\n      }\n    }\n  }, []);\n  const handleInputRef = useForkRef(inputRef, inputRefProp, inputPropsProp.ref, handleInputRefWarning);\n  const [focused, setFocused] = React.useState(false);\n  const muiFormControl = useFormControl();\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (muiFormControl) {\n        return muiFormControl.registerEffect();\n      }\n      return undefined;\n    }, [muiFormControl]);\n  }\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'disabled', 'error', 'hiddenLabel', 'size', 'required', 'filled']\n  });\n  fcs.focused = muiFormControl ? muiFormControl.focused : focused;\n\n  // The blur won't fire when the disabled state is set on a focused input.\n  // We need to book keep the focused state manually.\n  React.useEffect(() => {\n    if (!muiFormControl && disabled && focused) {\n      setFocused(false);\n      if (onBlur) {\n        onBlur();\n      }\n    }\n  }, [muiFormControl, disabled, focused, onBlur]);\n  const onFilled = muiFormControl && muiFormControl.onFilled;\n  const onEmpty = muiFormControl && muiFormControl.onEmpty;\n  const checkDirty = React.useCallback(obj => {\n    if (isFilled(obj)) {\n      if (onFilled) {\n        onFilled();\n      }\n    } else if (onEmpty) {\n      onEmpty();\n    }\n  }, [onFilled, onEmpty]);\n  useEnhancedEffect(() => {\n    if (isControlled) {\n      checkDirty({\n        value\n      });\n    }\n  }, [value, checkDirty, isControlled]);\n  const handleFocus = event => {\n    // Fix a bug with IE11 where the focus/blur events are triggered\n    // while the component is disabled.\n    if (fcs.disabled) {\n      event.stopPropagation();\n      return;\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n    if (inputPropsProp.onFocus) {\n      inputPropsProp.onFocus(event);\n    }\n    if (muiFormControl && muiFormControl.onFocus) {\n      muiFormControl.onFocus(event);\n    } else {\n      setFocused(true);\n    }\n  };\n  const handleBlur = event => {\n    if (onBlur) {\n      onBlur(event);\n    }\n    if (inputPropsProp.onBlur) {\n      inputPropsProp.onBlur(event);\n    }\n    if (muiFormControl && muiFormControl.onBlur) {\n      muiFormControl.onBlur(event);\n    } else {\n      setFocused(false);\n    }\n  };\n  const handleChange = (event, ...args) => {\n    if (!isControlled) {\n      const element = event.target || inputRef.current;\n      if (element == null) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Expected valid input target. Did you use a custom \\`inputComponent\\` and forget to forward refs? See https://mui.com/r/input-component-ref-interface for more info.` : _formatMuiErrorMessage(1));\n      }\n      checkDirty({\n        value: element.value\n      });\n    }\n    if (inputPropsProp.onChange) {\n      inputPropsProp.onChange(event, ...args);\n    }\n\n    // Perform in the willUpdate\n    if (onChange) {\n      onChange(event, ...args);\n    }\n  };\n\n  // Check the input state on mount, in case it was filled by the user\n  // or auto filled by the browser before the hydration (for SSR).\n  React.useEffect(() => {\n    checkDirty(inputRef.current);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const handleClick = event => {\n    if (inputRef.current && event.currentTarget === event.target) {\n      inputRef.current.focus();\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  let InputComponent = inputComponent;\n  let inputProps = inputPropsProp;\n  if (multiline && InputComponent === 'input') {\n    if (rows) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (minRows || maxRows) {\n          console.warn('MUI: You can not use the `minRows` or `maxRows` props when the input `rows` prop is set.');\n        }\n      }\n      inputProps = _extends({\n        type: undefined,\n        minRows: rows,\n        maxRows: rows\n      }, inputProps);\n    } else {\n      inputProps = _extends({\n        type: undefined,\n        maxRows,\n        minRows\n      }, inputProps);\n    }\n    InputComponent = TextareaAutosize;\n  }\n  const handleAutoFill = event => {\n    // Provide a fake value as Chrome might not let you access it for security reasons.\n    checkDirty(event.animationName === 'mui-auto-fill-cancel' ? inputRef.current : {\n      value: 'x'\n    });\n  };\n  React.useEffect(() => {\n    if (muiFormControl) {\n      muiFormControl.setAdornedStart(Boolean(startAdornment));\n    }\n  }, [muiFormControl, startAdornment]);\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    disabled: fcs.disabled,\n    endAdornment,\n    error: fcs.error,\n    focused: fcs.focused,\n    formControl: muiFormControl,\n    fullWidth,\n    hiddenLabel: fcs.hiddenLabel,\n    multiline,\n    size: fcs.size,\n    startAdornment,\n    type\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = slots.root || components.Root || InputBaseRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const Input = slots.input || components.Input || InputBaseComponent;\n  inputProps = _extends({}, inputProps, (_slotProps$input = slotProps.input) != null ? _slotProps$input : componentsProps.input);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [!disableInjectingGlobalStyles && inputGlobalStyles, /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, !isHostComponent(Root) && {\n      ownerState: _extends({}, ownerState, rootProps.ownerState)\n    }, {\n      ref: ref,\n      onClick: handleClick\n    }, other, {\n      className: clsx(classes.root, rootProps.className, className, readOnly && 'MuiInputBase-readOnly'),\n      children: [startAdornment, /*#__PURE__*/_jsx(FormControlContext.Provider, {\n        value: null,\n        children: /*#__PURE__*/_jsx(Input, _extends({\n          ownerState: ownerState,\n          \"aria-invalid\": fcs.error,\n          \"aria-describedby\": ariaDescribedby,\n          autoComplete: autoComplete,\n          autoFocus: autoFocus,\n          defaultValue: defaultValue,\n          disabled: fcs.disabled,\n          id: id,\n          onAnimationStart: handleAutoFill,\n          name: name,\n          placeholder: placeholder,\n          readOnly: readOnly,\n          required: fcs.required,\n          rows: rows,\n          value: value,\n          onKeyDown: onKeyDown,\n          onKeyUp: onKeyUp,\n          type: type\n        }, inputProps, !isHostComponent(Input) && {\n          as: InputComponent,\n          ownerState: _extends({}, ownerState, inputProps.ownerState)\n        }, {\n          ref: handleInputRef,\n          className: clsx(classes.input, inputProps.className, readOnly && 'MuiInputBase-readOnly'),\n          onBlur: handleBlur,\n          onChange: handleChange,\n          onFocus: handleFocus\n        }))\n      }), endAdornment, renderSuffix ? renderSuffix(_extends({}, fcs, {\n        startAdornment\n      })) : null]\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? InputBase.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * This prop helps users to fill forms faster, especially on mobile devices.\n   * The name can be confusing, as it's more like an autofill.\n   * You can learn more about it [following the specification](https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#autofill).\n   */\n  autoComplete: PropTypes.string,\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * The prop defaults to the value (`'primary'`) inherited from the parent FormControl component.\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Input: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    input: PropTypes.object,\n    root: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the component is disabled.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, GlobalStyles for the auto-fill keyframes will not be injected/removed on mount/unmount. Make sure to inject them at the top of your application.\n   * This option is intended to help with boosting the initial rendering performance if you are loading a big amount of Input components at once.\n   * @default false\n   */\n  disableInjectingGlobalStyles: PropTypes.bool,\n  /**\n   * End `InputAdornment` for this component.\n   */\n  endAdornment: PropTypes.node,\n  /**\n   * If `true`, the `input` will indicate an error.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the `input` will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * The component used for the `input` element.\n   * Either a string to use a HTML element or a component.\n   * @default 'input'\n   */\n  inputComponent: elementTypeAcceptingRef,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   * @default {}\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   * The prop defaults to the value (`'none'`) inherited from the parent FormControl component.\n   */\n  margin: PropTypes.oneOf(['dense', 'none']),\n  /**\n   * Maximum number of rows to display when multiline option is set to true.\n   */\n  maxRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Minimum number of rows to display when multiline option is set to true.\n   */\n  minRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * If `true`, a [TextareaAutosize](/material-ui/react-textarea-autosize/) element is rendered.\n   * @default false\n   */\n  multiline: PropTypes.bool,\n  /**\n   * Name attribute of the `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the `input` is blurred.\n   *\n   * Notice that the first argument (event) might be undefined.\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the value is changed.\n   *\n   * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the `input` doesn't satisfy its constraints.\n   */\n  onInvalid: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The short hint displayed in the `input` before the user enters a value.\n   */\n  placeholder: PropTypes.string,\n  /**\n   * It prevents the user from changing the value of the field\n   * (not from interacting with the field).\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  renderSuffix: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  required: PropTypes.bool,\n  /**\n   * Number of rows to display when multiline option is set to true.\n   */\n  rows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The size of the component.\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.object,\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * Start `InputAdornment` for this component.\n   */\n  startAdornment: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Type of the `input` element. It should be [a valid HTML5 input type](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types).\n   * @default 'text'\n   */\n  type: PropTypes.string,\n  /**\n   * The value of the `input` element, required for a controlled component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default InputBase;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,UAAU,EAAE,8BAA8B,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,CAAC;AAC3f,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,gBAAgB,IAAIC,wBAAwB,QAAQ,oBAAoB;AAC/E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACtD,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAED,UAAU,CAACE,WAAW,IAAIH,MAAM,CAACG,WAAW,EAAEF,UAAU,CAACG,cAAc,IAAIJ,MAAM,CAACK,YAAY,EAAEJ,UAAU,CAACK,YAAY,IAAIN,MAAM,CAACO,UAAU,EAAEN,UAAU,CAACO,KAAK,IAAIR,MAAM,CAACQ,KAAK,EAAEP,UAAU,CAACQ,IAAI,KAAK,OAAO,IAAIT,MAAM,CAACU,SAAS,EAAET,UAAU,CAACU,SAAS,IAAIX,MAAM,CAACW,SAAS,EAAEV,UAAU,CAACW,KAAK,IAAIZ,MAAM,CAAC,QAAQb,UAAU,CAACc,UAAU,CAACW,KAAK,CAAC,EAAE,CAAC,EAAEX,UAAU,CAACY,SAAS,IAAIb,MAAM,CAACa,SAAS,EAAEZ,UAAU,CAACa,WAAW,IAAId,MAAM,CAACc,WAAW,CAAC;AAC7b,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGA,CAAChB,KAAK,EAAEC,MAAM,KAAK;EACvD,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO,CAACC,MAAM,CAACgB,KAAK,EAAEf,UAAU,CAACQ,IAAI,KAAK,OAAO,IAAIT,MAAM,CAACiB,cAAc,EAAEhB,UAAU,CAACU,SAAS,IAAIX,MAAM,CAACkB,cAAc,EAAEjB,UAAU,CAACkB,IAAI,KAAK,QAAQ,IAAInB,MAAM,CAACoB,eAAe,EAAEnB,UAAU,CAACG,cAAc,IAAIJ,MAAM,CAACqB,iBAAiB,EAAEpB,UAAU,CAACK,YAAY,IAAIN,MAAM,CAACsB,eAAe,EAAErB,UAAU,CAACa,WAAW,IAAId,MAAM,CAACuB,gBAAgB,CAAC;AACjV,CAAC;AACD,MAAMC,iBAAiB,GAAGvB,UAAU,IAAI;EACtC,MAAM;IACJwB,OAAO;IACPb,KAAK;IACLc,QAAQ;IACRlB,KAAK;IACLF,YAAY;IACZqB,OAAO;IACPxB,WAAW;IACXU,SAAS;IACTC,WAAW;IACXH,SAAS;IACTiB,QAAQ;IACRnB,IAAI;IACJL,cAAc;IACde;EACF,CAAC,GAAGlB,UAAU;EACd,MAAM4B,KAAK,GAAG;IACZ3B,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQf,UAAU,CAACyB,KAAK,CAAC,EAAE,EAAEc,QAAQ,IAAI,UAAU,EAAElB,KAAK,IAAI,OAAO,EAAEK,SAAS,IAAI,WAAW,EAAEc,OAAO,IAAI,SAAS,EAAExB,WAAW,IAAI,aAAa,EAAEM,IAAI,IAAIA,IAAI,KAAK,QAAQ,IAAI,OAAOtB,UAAU,CAACsB,IAAI,CAAC,EAAE,EAAEE,SAAS,IAAI,WAAW,EAAEP,cAAc,IAAI,cAAc,EAAEE,YAAY,IAAI,YAAY,EAAEQ,WAAW,IAAI,aAAa,EAAEc,QAAQ,IAAI,UAAU,CAAC;IAC3WZ,KAAK,EAAE,CAAC,OAAO,EAAEU,QAAQ,IAAI,UAAU,EAAEP,IAAI,KAAK,QAAQ,IAAI,iBAAiB,EAAER,SAAS,IAAI,gBAAgB,EAAEF,IAAI,KAAK,OAAO,IAAI,gBAAgB,EAAEK,WAAW,IAAI,kBAAkB,EAAEV,cAAc,IAAI,mBAAmB,EAAEE,YAAY,IAAI,iBAAiB,EAAEsB,QAAQ,IAAI,UAAU;EAC3R,CAAC;EACD,OAAOjD,cAAc,CAACkD,KAAK,EAAEpC,wBAAwB,EAAEgC,OAAO,CAAC;AACjE,CAAC;AACD,OAAO,MAAMK,aAAa,GAAG7C,MAAM,CAAC,KAAK,EAAE;EACzC8C,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEnC;AACrB,CAAC,CAAC,CAAC,CAAC;EACFoC,KAAK;EACLjC;AACF,CAAC,KAAK9B,QAAQ,CAAC,CAAC,CAAC,EAAE+D,KAAK,CAACC,UAAU,CAACC,KAAK,EAAE;EACzCxB,KAAK,EAAE,CAACsB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,IAAI,CAACC,OAAO;EACjDC,UAAU,EAAE,UAAU;EACtB;EACAC,SAAS,EAAE,YAAY;EACvB;EACAC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,aAAa;EACtBC,UAAU,EAAE,QAAQ;EACpB,CAAC,KAAKtD,gBAAgB,CAACkC,QAAQ,EAAE,GAAG;IAClCd,KAAK,EAAE,CAACsB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,IAAI,CAACb,QAAQ;IAClDkB,MAAM,EAAE;EACV;AACF,CAAC,EAAE3C,UAAU,CAACU,SAAS,IAAIxC,QAAQ,CAAC;EAClC4E,OAAO,EAAE;AACX,CAAC,EAAE9C,UAAU,CAACQ,IAAI,KAAK,OAAO,IAAI;EAChCuC,UAAU,EAAE;AACd,CAAC,CAAC,EAAE/C,UAAU,CAACY,SAAS,IAAI;EAC1BoC,KAAK,EAAE;AACT,CAAC,CAAC,CAAC;AACH,OAAO,MAAMC,kBAAkB,GAAGjE,MAAM,CAAC,OAAO,EAAE;EAChD8C,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAElB;AACrB,CAAC,CAAC,CAAC,CAAC;EACFmB,KAAK;EACLjC;AACF,CAAC,KAAK;EACJ,MAAMkD,KAAK,GAAGjB,KAAK,CAACI,OAAO,CAACc,IAAI,KAAK,OAAO;EAC5C,MAAMC,WAAW,GAAGlF,QAAQ,CAAC;IAC3ByC,KAAK,EAAE;EACT,CAAC,EAAEsB,KAAK,CAACG,IAAI,GAAG;IACdiB,OAAO,EAAEpB,KAAK,CAACG,IAAI,CAACiB,OAAO,CAACC;EAC9B,CAAC,GAAG;IACFD,OAAO,EAAEH,KAAK,GAAG,IAAI,GAAG;EAC1B,CAAC,EAAE;IACDK,UAAU,EAAEtB,KAAK,CAACuB,WAAW,CAACC,MAAM,CAAC,SAAS,EAAE;MAC9CC,QAAQ,EAAEzB,KAAK,CAACuB,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC,CAAC;EACF,MAAMC,iBAAiB,GAAG;IACxBP,OAAO,EAAE;EACX,CAAC;EACD,MAAMQ,kBAAkB,GAAG5B,KAAK,CAACG,IAAI,GAAG;IACtCiB,OAAO,EAAEpB,KAAK,CAACG,IAAI,CAACiB,OAAO,CAACC;EAC9B,CAAC,GAAG;IACFD,OAAO,EAAEH,KAAK,GAAG,IAAI,GAAG;EAC1B,CAAC;EACD,OAAOhF,QAAQ,CAAC;IACd4F,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBpD,KAAK,EAAE,cAAc;IACrBmC,OAAO,EAAE,WAAW;IACpBkB,MAAM,EAAE,CAAC;IACTvB,SAAS,EAAE,aAAa;IACxBwB,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,UAAU;IAClB;IACAC,MAAM,EAAE,CAAC;IACT;IACAC,uBAAuB,EAAE,aAAa;IACtCxB,OAAO,EAAE,OAAO;IAChB;IACAyB,QAAQ,EAAE,CAAC;IACXrB,KAAK,EAAE,MAAM;IACb;IACAsB,aAAa,EAAE,sBAAsB;IACrCC,iBAAiB,EAAE,MAAM;IACzB,8BAA8B,EAAEnB,WAAW;IAC3C,qBAAqB,EAAEA,WAAW;IAClC;IACA,yBAAyB,EAAEA,WAAW;IACtC;IACA,0BAA0B,EAAEA,WAAW;IACvC;IACA,SAAS,EAAE;MACToB,OAAO,EAAE;IACX,CAAC;IACD;IACA,WAAW,EAAE;MACXC,SAAS,EAAE;IACb,CAAC;IACD,8BAA8B,EAAE;MAC9B;MACAC,gBAAgB,EAAE;IACpB,CAAC;IACD;IACA,CAAC,+BAA+BnF,gBAAgB,CAACW,WAAW,IAAI,GAAG;MACjE,8BAA8B,EAAE0D,iBAAiB;MACjD,qBAAqB,EAAEA,iBAAiB;MACxC;MACA,yBAAyB,EAAEA,iBAAiB;MAC5C;MACA,0BAA0B,EAAEA,iBAAiB;MAC7C;MACA,oCAAoC,EAAEC,kBAAkB;MACxD,2BAA2B,EAAEA,kBAAkB;MAC/C;MACA,+BAA+B,EAAEA,kBAAkB;MACnD;MACA,gCAAgC,EAAEA,kBAAkB,CAAC;IACvD,CAAC;IACD,CAAC,KAAKtE,gBAAgB,CAACkC,QAAQ,EAAE,GAAG;MAClC4B,OAAO,EAAE,CAAC;MACV;MACAsB,mBAAmB,EAAE,CAAC1C,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,IAAI,CAACb,QAAQ,CAAC;IACnE,CAAC;IACD,oBAAoB,EAAE;MACpB8C,iBAAiB,EAAE,OAAO;MAC1BD,aAAa,EAAE;IACjB;EACF,CAAC,EAAEtE,UAAU,CAACQ,IAAI,KAAK,OAAO,IAAI;IAChCuC,UAAU,EAAE;EACd,CAAC,EAAE/C,UAAU,CAACU,SAAS,IAAI;IACzBwD,MAAM,EAAE,MAAM;IACdU,MAAM,EAAE,MAAM;IACd9B,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE;EACd,CAAC,EAAE/C,UAAU,CAACkB,IAAI,KAAK,QAAQ,IAAI;IACjC;IACA2D,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMC,iBAAiB,GAAG,aAAapF,IAAI,CAACL,YAAY,EAAE;EACxDU,MAAM,EAAE;IACN,0BAA0B,EAAE;MAC1BgF,IAAI,EAAE;QACJnC,OAAO,EAAE;MACX;IACF,CAAC;IACD,iCAAiC,EAAE;MACjCmC,IAAI,EAAE;QACJnC,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,MAAMoC,SAAS,GAAG,aAAa3G,KAAK,CAAC4G,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,IAAIC,gBAAgB;EACpB,MAAMtF,KAAK,GAAGb,eAAe,CAAC;IAC5Ba,KAAK,EAAEoF,OAAO;IACdpD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF,kBAAkB,EAAEuD,eAAe;MACnCC,YAAY;MACZC,SAAS;MACTC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,YAAY;MACZlE,QAAQ;MACRmE,4BAA4B;MAC5BvF,YAAY;MACZO,SAAS,GAAG,KAAK;MACjBiF,EAAE;MACFC,cAAc,GAAG,OAAO;MACxBC,UAAU,EAAEC,cAAc,GAAG,CAAC,CAAC;MAC/BC,QAAQ,EAAEC,YAAY;MACtBC,OAAO;MACPC,OAAO;MACP1F,SAAS,GAAG,KAAK;MACjBoB,IAAI;MACJuE,MAAM;MACNC,QAAQ;MACRC,OAAO;MACPC,OAAO;MACPC,SAAS;MACTC,OAAO;MACPtD,WAAW;MACXzB,QAAQ;MACRgF,YAAY;MACZC,IAAI;MACJC,SAAS,GAAG,CAAC,CAAC;MACdjF,KAAK,GAAG,CAAC,CAAC;MACVzB,cAAc;MACde,IAAI,GAAG,MAAM;MACb4F,KAAK,EAAEC;IACT,CAAC,GAAGjH,KAAK;IACTkH,KAAK,GAAG/I,6BAA6B,CAAC6B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM0I,KAAK,GAAGd,cAAc,CAACc,KAAK,IAAI,IAAI,GAAGd,cAAc,CAACc,KAAK,GAAGC,SAAS;EAC7E,MAAM;IACJE,OAAO,EAAEC;EACX,CAAC,GAAG7I,KAAK,CAAC8I,MAAM,CAACL,KAAK,IAAI,IAAI,CAAC;EAC/B,MAAMb,QAAQ,GAAG5H,KAAK,CAAC8I,MAAM,CAAC,CAAC;EAC/B,MAAMC,qBAAqB,GAAG/I,KAAK,CAACgJ,WAAW,CAACC,QAAQ,IAAI;IAC1D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIH,QAAQ,IAAIA,QAAQ,CAACI,QAAQ,KAAK,OAAO,IAAI,CAACJ,QAAQ,CAACK,KAAK,EAAE;QAChEC,OAAO,CAACrH,KAAK,CAAC,CAAC,kEAAkE,EAAE,gDAAgD,EAAE,6DAA6D,CAAC,CAACsH,IAAI,CAAC,IAAI,CAAC,CAAC;MACjN;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,cAAc,GAAG3I,UAAU,CAAC8G,QAAQ,EAAEC,YAAY,EAAEF,cAAc,CAACb,GAAG,EAAEiC,qBAAqB,CAAC;EACpG,MAAM,CAAC1F,OAAO,EAAEqG,UAAU,CAAC,GAAG1J,KAAK,CAAC2J,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMC,cAAc,GAAGlJ,cAAc,CAAC,CAAC;EACvC,IAAIwI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACApJ,KAAK,CAAC6J,SAAS,CAAC,MAAM;MACpB,IAAID,cAAc,EAAE;QAClB,OAAOA,cAAc,CAACE,cAAc,CAAC,CAAC;MACxC;MACA,OAAOC,SAAS;IAClB,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;EACtB;EACA,MAAMI,GAAG,GAAGxJ,gBAAgB,CAAC;IAC3BiB,KAAK;IACLmI,cAAc;IACdK,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;EACpF,CAAC,CAAC;EACFD,GAAG,CAAC3G,OAAO,GAAGuG,cAAc,GAAGA,cAAc,CAACvG,OAAO,GAAGA,OAAO;;EAE/D;EACA;EACArD,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAI,CAACD,cAAc,IAAIxG,QAAQ,IAAIC,OAAO,EAAE;MAC1CqG,UAAU,CAAC,KAAK,CAAC;MACjB,IAAI1B,MAAM,EAAE;QACVA,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC,EAAE,CAAC4B,cAAc,EAAExG,QAAQ,EAAEC,OAAO,EAAE2E,MAAM,CAAC,CAAC;EAC/C,MAAMkC,QAAQ,GAAGN,cAAc,IAAIA,cAAc,CAACM,QAAQ;EAC1D,MAAMC,OAAO,GAAGP,cAAc,IAAIA,cAAc,CAACO,OAAO;EACxD,MAAMC,UAAU,GAAGpK,KAAK,CAACgJ,WAAW,CAACqB,GAAG,IAAI;IAC1C,IAAIpJ,QAAQ,CAACoJ,GAAG,CAAC,EAAE;MACjB,IAAIH,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM,IAAIC,OAAO,EAAE;MAClBA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACD,QAAQ,EAAEC,OAAO,CAAC,CAAC;EACvBpJ,iBAAiB,CAAC,MAAM;IACtB,IAAI8H,YAAY,EAAE;MAChBuB,UAAU,CAAC;QACT3B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,KAAK,EAAE2B,UAAU,EAAEvB,YAAY,CAAC,CAAC;EACrC,MAAMyB,WAAW,GAAGC,KAAK,IAAI;IAC3B;IACA;IACA,IAAIP,GAAG,CAAC5G,QAAQ,EAAE;MAChBmH,KAAK,CAACC,eAAe,CAAC,CAAC;MACvB;IACF;IACA,IAAIrC,OAAO,EAAE;MACXA,OAAO,CAACoC,KAAK,CAAC;IAChB;IACA,IAAI5C,cAAc,CAACQ,OAAO,EAAE;MAC1BR,cAAc,CAACQ,OAAO,CAACoC,KAAK,CAAC;IAC/B;IACA,IAAIX,cAAc,IAAIA,cAAc,CAACzB,OAAO,EAAE;MAC5CyB,cAAc,CAACzB,OAAO,CAACoC,KAAK,CAAC;IAC/B,CAAC,MAAM;MACLb,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC;EACD,MAAMe,UAAU,GAAGF,KAAK,IAAI;IAC1B,IAAIvC,MAAM,EAAE;MACVA,MAAM,CAACuC,KAAK,CAAC;IACf;IACA,IAAI5C,cAAc,CAACK,MAAM,EAAE;MACzBL,cAAc,CAACK,MAAM,CAACuC,KAAK,CAAC;IAC9B;IACA,IAAIX,cAAc,IAAIA,cAAc,CAAC5B,MAAM,EAAE;MAC3C4B,cAAc,CAAC5B,MAAM,CAACuC,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMgB,YAAY,GAAGA,CAACH,KAAK,EAAE,GAAGI,IAAI,KAAK;IACvC,IAAI,CAAC9B,YAAY,EAAE;MACjB,MAAM+B,OAAO,GAAGL,KAAK,CAACM,MAAM,IAAIjD,QAAQ,CAACgB,OAAO;MAChD,IAAIgC,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIE,KAAK,CAAC5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,0KAA0K,GAAGtJ,sBAAsB,CAAC,CAAC,CAAC,CAAC;MACjQ;MACAsK,UAAU,CAAC;QACT3B,KAAK,EAAEmC,OAAO,CAACnC;MACjB,CAAC,CAAC;IACJ;IACA,IAAId,cAAc,CAACM,QAAQ,EAAE;MAC3BN,cAAc,CAACM,QAAQ,CAACsC,KAAK,EAAE,GAAGI,IAAI,CAAC;IACzC;;IAEA;IACA,IAAI1C,QAAQ,EAAE;MACZA,QAAQ,CAACsC,KAAK,EAAE,GAAGI,IAAI,CAAC;IAC1B;EACF,CAAC;;EAED;EACA;EACA3K,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpBO,UAAU,CAACxC,QAAQ,CAACgB,OAAO,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMmC,WAAW,GAAGR,KAAK,IAAI;IAC3B,IAAI3C,QAAQ,CAACgB,OAAO,IAAI2B,KAAK,CAACS,aAAa,KAAKT,KAAK,CAACM,MAAM,EAAE;MAC5DjD,QAAQ,CAACgB,OAAO,CAACU,KAAK,CAAC,CAAC;IAC1B;IACA,IAAIpB,OAAO,EAAE;MACXA,OAAO,CAACqC,KAAK,CAAC;IAChB;EACF,CAAC;EACD,IAAIU,cAAc,GAAGxD,cAAc;EACnC,IAAIC,UAAU,GAAGC,cAAc;EAC/B,IAAItF,SAAS,IAAI4I,cAAc,KAAK,OAAO,EAAE;IAC3C,IAAI1C,IAAI,EAAE;MACR,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIrB,OAAO,IAAID,OAAO,EAAE;UACtByB,OAAO,CAAC2B,IAAI,CAAC,0FAA0F,CAAC;QAC1G;MACF;MACAxD,UAAU,GAAG7H,QAAQ,CAAC;QACpBgD,IAAI,EAAEkH,SAAS;QACfhC,OAAO,EAAEQ,IAAI;QACbT,OAAO,EAAES;MACX,CAAC,EAAEb,UAAU,CAAC;IAChB,CAAC,MAAM;MACLA,UAAU,GAAG7H,QAAQ,CAAC;QACpBgD,IAAI,EAAEkH,SAAS;QACfjC,OAAO;QACPC;MACF,CAAC,EAAEL,UAAU,CAAC;IAChB;IACAuD,cAAc,GAAG1K,gBAAgB;EACnC;EACA,MAAM4K,cAAc,GAAGZ,KAAK,IAAI;IAC9B;IACAH,UAAU,CAACG,KAAK,CAACtE,aAAa,KAAK,sBAAsB,GAAG2B,QAAQ,CAACgB,OAAO,GAAG;MAC7EH,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACDzI,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAID,cAAc,EAAE;MAClBA,cAAc,CAACwB,eAAe,CAACC,OAAO,CAACvJ,cAAc,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAAC8H,cAAc,EAAE9H,cAAc,CAAC,CAAC;EACpC,MAAMH,UAAU,GAAG9B,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCa,KAAK,EAAE0H,GAAG,CAAC1H,KAAK,IAAI,SAAS;IAC7Bc,QAAQ,EAAE4G,GAAG,CAAC5G,QAAQ;IACtBpB,YAAY;IACZE,KAAK,EAAE8H,GAAG,CAAC9H,KAAK;IAChBmB,OAAO,EAAE2G,GAAG,CAAC3G,OAAO;IACpBxB,WAAW,EAAE+H,cAAc;IAC3BrH,SAAS;IACTC,WAAW,EAAEwH,GAAG,CAACxH,WAAW;IAC5BH,SAAS;IACTF,IAAI,EAAE6H,GAAG,CAAC7H,IAAI;IACdL,cAAc;IACde;EACF,CAAC,CAAC;EACF,MAAMM,OAAO,GAAGD,iBAAiB,CAACvB,UAAU,CAAC;EAC7C,MAAM2J,IAAI,GAAG/H,KAAK,CAAC3B,IAAI,IAAIwF,UAAU,CAACkE,IAAI,IAAI9H,aAAa;EAC3D,MAAM+H,SAAS,GAAG/C,SAAS,CAAC5G,IAAI,IAAIyF,eAAe,CAACzF,IAAI,IAAI,CAAC,CAAC;EAC9D,MAAM4J,KAAK,GAAGjI,KAAK,CAACb,KAAK,IAAI0E,UAAU,CAACoE,KAAK,IAAI5G,kBAAkB;EACnE8C,UAAU,GAAG7H,QAAQ,CAAC,CAAC,CAAC,EAAE6H,UAAU,EAAE,CAACX,gBAAgB,GAAGyB,SAAS,CAAC9F,KAAK,KAAK,IAAI,GAAGqE,gBAAgB,GAAGM,eAAe,CAAC3E,KAAK,CAAC;EAC9H,OAAO,aAAanB,KAAK,CAACvB,KAAK,CAACyL,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,CAACnE,4BAA4B,IAAId,iBAAiB,EAAE,aAAalF,KAAK,CAAC+J,IAAI,EAAEzL,QAAQ,CAAC,CAAC,CAAC,EAAE0L,SAAS,EAAE,CAACjL,eAAe,CAACgL,IAAI,CAAC,IAAI;MACxI3J,UAAU,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,UAAU,EAAE4J,SAAS,CAAC5J,UAAU;IAC3D,CAAC,EAAE;MACDmF,GAAG,EAAEA,GAAG;MACRoB,OAAO,EAAE6C;IACX,CAAC,EAAEpC,KAAK,EAAE;MACRxB,SAAS,EAAEjH,IAAI,CAACiD,OAAO,CAACvB,IAAI,EAAE2J,SAAS,CAACpE,SAAS,EAAEA,SAAS,EAAE7D,QAAQ,IAAI,uBAAuB,CAAC;MAClGoI,QAAQ,EAAE,CAAC5J,cAAc,EAAE,aAAaT,IAAI,CAACZ,kBAAkB,CAACkL,QAAQ,EAAE;QACxElD,KAAK,EAAE,IAAI;QACXiD,QAAQ,EAAE,aAAarK,IAAI,CAACmK,KAAK,EAAE3L,QAAQ,CAAC;UAC1C8B,UAAU,EAAEA,UAAU;UACtB,cAAc,EAAEqI,GAAG,CAAC9H,KAAK;UACzB,kBAAkB,EAAE8E,eAAe;UACnCC,YAAY,EAAEA,YAAY;UAC1BC,SAAS,EAAEA,SAAS;UACpBI,YAAY,EAAEA,YAAY;UAC1BlE,QAAQ,EAAE4G,GAAG,CAAC5G,QAAQ;UACtBoE,EAAE,EAAEA,EAAE;UACNoE,gBAAgB,EAAET,cAAc;UAChC1H,IAAI,EAAEA,IAAI;UACVsB,WAAW,EAAEA,WAAW;UACxBzB,QAAQ,EAAEA,QAAQ;UAClBuI,QAAQ,EAAE7B,GAAG,CAAC6B,QAAQ;UACtBtD,IAAI,EAAEA,IAAI;UACVE,KAAK,EAAEA,KAAK;UACZL,SAAS,EAAEA,SAAS;UACpBC,OAAO,EAAEA,OAAO;UAChBxF,IAAI,EAAEA;QACR,CAAC,EAAE6E,UAAU,EAAE,CAACpH,eAAe,CAACkL,KAAK,CAAC,IAAI;UACxCM,EAAE,EAAEb,cAAc;UAClBtJ,UAAU,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,UAAU,EAAE+F,UAAU,CAAC/F,UAAU;QAC5D,CAAC,EAAE;UACDmF,GAAG,EAAE2C,cAAc;UACnBtC,SAAS,EAAEjH,IAAI,CAACiD,OAAO,CAACT,KAAK,EAAEgF,UAAU,CAACP,SAAS,EAAE7D,QAAQ,IAAI,uBAAuB,CAAC;UACzF0E,MAAM,EAAEyC,UAAU;UAClBxC,QAAQ,EAAEyC,YAAY;UACtBvC,OAAO,EAAEmC;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,EAAEtI,YAAY,EAAEsG,YAAY,GAAGA,YAAY,CAACzI,QAAQ,CAAC,CAAC,CAAC,EAAEmK,GAAG,EAAE;QAC9DlI;MACF,CAAC,CAAC,CAAC,GAAG,IAAI;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFoH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,SAAS,CAACoF,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACE,kBAAkB,EAAE9L,SAAS,CAAC+L,MAAM;EACpC;AACF;AACA;AACA;AACA;EACE/E,YAAY,EAAEhH,SAAS,CAAC+L,MAAM;EAC9B;AACF;AACA;EACE9E,SAAS,EAAEjH,SAAS,CAACgM,IAAI;EACzB;AACF;AACA;EACE9I,OAAO,EAAElD,SAAS,CAACiM,MAAM;EACzB;AACF;AACA;EACE/E,SAAS,EAAElH,SAAS,CAAC+L,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACE1J,KAAK,EAAErC,SAAS,CAAC,sCAAsCkM,SAAS,CAAC,CAAClM,SAAS,CAACmM,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEnM,SAAS,CAAC+L,MAAM,CAAC,CAAC;EACtK;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE5E,UAAU,EAAEnH,SAAS,CAACoM,KAAK,CAAC;IAC1Bb,KAAK,EAAEvL,SAAS,CAACqM,WAAW;IAC5BhB,IAAI,EAAErL,SAAS,CAACqM;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjF,eAAe,EAAEpH,SAAS,CAACoM,KAAK,CAAC;IAC/B3J,KAAK,EAAEzC,SAAS,CAACiM,MAAM;IACvBtK,IAAI,EAAE3B,SAAS,CAACiM;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE5E,YAAY,EAAErH,SAAS,CAACsM,GAAG;EAC3B;AACF;AACA;AACA;EACEnJ,QAAQ,EAAEnD,SAAS,CAACgM,IAAI;EACxB;AACF;AACA;AACA;AACA;EACE1E,4BAA4B,EAAEtH,SAAS,CAACgM,IAAI;EAC5C;AACF;AACA;EACEjK,YAAY,EAAE/B,SAAS,CAACuM,IAAI;EAC5B;AACF;AACA;AACA;EACEtK,KAAK,EAAEjC,SAAS,CAACgM,IAAI;EACrB;AACF;AACA;AACA;EACE1J,SAAS,EAAEtC,SAAS,CAACgM,IAAI;EACzB;AACF;AACA;EACEzE,EAAE,EAAEvH,SAAS,CAAC+L,MAAM;EACpB;AACF;AACA;AACA;AACA;EACEvE,cAAc,EAAEtH,uBAAuB;EACvC;AACF;AACA;AACA;EACEuH,UAAU,EAAEzH,SAAS,CAACiM,MAAM;EAC5B;AACF;AACA;EACEtE,QAAQ,EAAExH,OAAO;EACjB;AACF;AACA;AACA;AACA;EACE0F,MAAM,EAAE7F,SAAS,CAACmM,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC1C;AACF;AACA;EACEtE,OAAO,EAAE7H,SAAS,CAACkM,SAAS,CAAC,CAAClM,SAAS,CAACwM,MAAM,EAAExM,SAAS,CAAC+L,MAAM,CAAC,CAAC;EAClE;AACF;AACA;EACEjE,OAAO,EAAE9H,SAAS,CAACkM,SAAS,CAAC,CAAClM,SAAS,CAACwM,MAAM,EAAExM,SAAS,CAAC+L,MAAM,CAAC,CAAC;EAClE;AACF;AACA;AACA;EACE3J,SAAS,EAAEpC,SAAS,CAACgM,IAAI;EACzB;AACF;AACA;EACExI,IAAI,EAAExD,SAAS,CAAC+L,MAAM;EACtB;AACF;AACA;AACA;AACA;EACEhE,MAAM,EAAE/H,SAAS,CAACyM,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;EACEzE,QAAQ,EAAEhI,SAAS,CAACyM,IAAI;EACxB;AACF;AACA;EACExE,OAAO,EAAEjI,SAAS,CAACyM,IAAI;EACvB;AACF;AACA;EACEvE,OAAO,EAAElI,SAAS,CAACyM,IAAI;EACvB;AACF;AACA;EACEC,SAAS,EAAE1M,SAAS,CAACyM,IAAI;EACzB;AACF;AACA;EACEtE,SAAS,EAAEnI,SAAS,CAACyM,IAAI;EACzB;AACF;AACA;EACErE,OAAO,EAAEpI,SAAS,CAACyM,IAAI;EACvB;AACF;AACA;EACE3H,WAAW,EAAE9E,SAAS,CAAC+L,MAAM;EAC7B;AACF;AACA;AACA;EACE1I,QAAQ,EAAErD,SAAS,CAACgM,IAAI;EACxB;AACF;AACA;EACE3D,YAAY,EAAErI,SAAS,CAACyM,IAAI;EAC5B;AACF;AACA;AACA;EACEb,QAAQ,EAAE5L,SAAS,CAACgM,IAAI;EACxB;AACF;AACA;EACE1D,IAAI,EAAEtI,SAAS,CAACkM,SAAS,CAAC,CAAClM,SAAS,CAACwM,MAAM,EAAExM,SAAS,CAAC+L,MAAM,CAAC,CAAC;EAC/D;AACF;AACA;EACE7J,IAAI,EAAElC,SAAS,CAAC,sCAAsCkM,SAAS,CAAC,CAAClM,SAAS,CAACmM,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEnM,SAAS,CAAC+L,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACExD,SAAS,EAAEvI,SAAS,CAACoM,KAAK,CAAC;IACzB3J,KAAK,EAAEzC,SAAS,CAACiM,MAAM;IACvBtK,IAAI,EAAE3B,SAAS,CAACiM;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACE3I,KAAK,EAAEtD,SAAS,CAACoM,KAAK,CAAC;IACrB3J,KAAK,EAAEzC,SAAS,CAACqM,WAAW;IAC5B1K,IAAI,EAAE3B,SAAS,CAACqM;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACExK,cAAc,EAAE7B,SAAS,CAACuM,IAAI;EAC9B;AACF;AACA;EACEI,EAAE,EAAE3M,SAAS,CAACkM,SAAS,CAAC,CAAClM,SAAS,CAAC4M,OAAO,CAAC5M,SAAS,CAACkM,SAAS,CAAC,CAAClM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACiM,MAAM,EAAEjM,SAAS,CAACgM,IAAI,CAAC,CAAC,CAAC,EAAEhM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACiM,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACErJ,IAAI,EAAE5C,SAAS,CAAC+L,MAAM;EACtB;AACF;AACA;EACEvD,KAAK,EAAExI,SAAS,CAACsM;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe5F,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}