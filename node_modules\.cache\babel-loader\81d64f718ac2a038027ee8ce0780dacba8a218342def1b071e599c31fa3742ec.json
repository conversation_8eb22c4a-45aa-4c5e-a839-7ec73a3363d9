{"ast": null, "code": "'use client';\n\nexport { default } from './NativeSelect';\nexport { default as nativeSelectClasses } from './nativeSelectClasses';\nexport * from './nativeSelectClasses';", "map": {"version": 3, "names": ["default", "nativeSelectClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/NativeSelect/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './NativeSelect';\nexport { default as nativeSelectClasses } from './nativeSelectClasses';\nexport * from './nativeSelectClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,mBAAmB,QAAQ,uBAAuB;AACtE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}