{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAlertTitleUtilityClass(slot) {\n  return generateUtilityClass('MuiAlertTitle', slot);\n}\nconst alertTitleClasses = generateUtilityClasses('MuiAlertTitle', ['root']);\nexport default alertTitleClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getAlertTitleUtilityClass", "slot", "alertTitleClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/AlertTitle/alertTitleClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAlertTitleUtilityClass(slot) {\n  return generateUtilityClass('MuiAlertTitle', slot);\n}\nconst alertTitleClasses = generateUtilityClasses('MuiAlertTitle', ['root']);\nexport default alertTitleClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC;AAC3E,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}