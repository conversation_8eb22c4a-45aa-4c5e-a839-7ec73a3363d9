{"ast": null, "code": "'use client';\n\nexport { default } from './ScopedCssBaseline';\nexport { default as scopedCssBaselineClasses } from './scopedCssBaselineClasses';\nexport * from './scopedCssBaselineClasses';", "map": {"version": 3, "names": ["default", "scopedCssBaselineClasses"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/@mui/material/ScopedCssBaseline/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './ScopedCssBaseline';\nexport { default as scopedCssBaselineClasses } from './scopedCssBaselineClasses';\nexport * from './scopedCssBaselineClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,4BAA4B;AAChF,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}